#include <errno.h>
#include <system/kconfig.h>
#include <net/ethernet_dev.h>
#include <net/if_arp.h>
#include <netinet/if_ether.h>
#include <net/netdev.h>
#include <net/packet.h>

#include <symtab.h>

#if IS_ENABLED(CONFIG_LWIP_2_2_0)
#include <lwip/netif.h>
#include <lwip/pbuf.h>
#include <net/lwip_ethernet.h>
#endif

KSYM_EXPORT(ETH_DATA_TO_STACK);

/* 数据传递至协议栈 */
void ETH_DATA_TO_STACK(ETH_DEV *ethdev, ETH_NETPKT *netpkt)
{
#ifdef CONFIG_SUPPORT_PCAP_TOOL
    struct pcap_nsock *pnsock;

    if (TTOS_OK == TTOS_ObtainMutex(PCAP_SOCKLIST_MUTEX, TTOS_MUTEX_WAIT_FOREVER))
    {
        list_for_each_entry(pnsock, &PCAP_NSOCK_LIST, node)
        {
            if (ethdev == pnsock->eth)
            {
                TTOS_SendMsgq(pnsock->msgq, netpkt->buf, netpkt->len, 0, 0);
            }
        }
        TTOS_ReleaseMutex(PCAP_SOCKLIST_MUTEX);
    }
#endif

#if defined(CONFIG_TCPIP)
#if defined(CONFIG_SUPPORT_AF_PACKET)
    uint16_t pptp_type;

    pptp_type = eth_network_protocol_parse((ether_header_t *)netpkt->buf);
    switch (pptp_type)
    {
        case ETH_P_ARP:
#if defined(CONFIG_SUPPORT_AF_PACKET_RECV_ARP) || defined(CONFIG_SUPPORT_AF_PACKET_RECV_ALL)
            packet_input(ethdev, netpkt);
#endif
            break;
        case ETH_P_IP:
#if defined(CONFIG_SUPPORT_AF_PACKET_RECV_ALL)
            packet_input(ethdev, netpkt);
#endif
            break;
        default:
            packet_input(ethdev, netpkt);
            break;
    }
#endif /* CONFIG_SUPPORT_AF_PACKET */

#if IS_ENABLED(CONFIG_LWIP_2_2_0)
    eth_lwip_data_to_stack(ethdev, netpkt);
#endif /* IS_ENABLED(CONFIG_LWIP_2_2_0) */
#else
    packet_input(ethdev, netpkt);
#endif /* IS_ENABLED(CONFIG_TCPIP) */
}
