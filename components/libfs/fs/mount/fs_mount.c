/****************************************************************************
 * fs/mount/fs_mount.c
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

/****************************************************************************
 * Included Files
 ****************************************************************************/
#include <assert.h>
#include <errno.h>
#include <list.h>
#include <stdbool.h>
#include <stdio.h>
#include <stdlib.h>
#define _POSIX_SOURCE 1
#include <string.h>
#include <sys/mount.h>
#include <sys/stat.h>
#include <unistd.h>

#include "../driver/driver.h"
#include "../inode/inode.h"

#include <arpa/inet.h>
#include <net/if.h>
#include <netinet/in.h>
#include <fs/nfs.h>

#define KLOG_TAG "mount"
#include <klog.h>

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/* Configuration ************************************************************/

/* In the canonical case, a file system is bound to a block driver.  However,
 * some less typical cases a block driver is not required.  Examples are
 * pseudo file systems (like BINFS or PROCFS) and MTD file systems (like
 * NXFFS).
 *
 * These file systems all require block drivers:
 */

#if defined(CONFIG_FS_FAT) || defined(CONFIG_FS_ROMFS)                         \
    || defined(CONFIG_FS_SMARTFS) || defined(CONFIG_FS_LITTLEFS)               \
    || defined(CONFIG_FS_LWEXT4)
#define BDFS_SUPPORT 1
#endif

/* These file systems require MTD drivers */

#if (defined(CONFIG_FS_SPIFFS) || defined(CONFIG_FS_LITTLEFS))                 \
    && defined(CONFIG_MTD)
#define MDFS_SUPPORT 1
#endif

/* These file systems do not require block or MTD drivers */

#if defined(CONFIG_FS_PROCFS) || defined(CONFIG_NFS)                           \
    || defined(CONFIG_FS_TMPFS) || defined(CONFIG_FS_USERFS)                   \
    || defined(CONFIG_FS_CROMFS) || defined(CONFIG_FS_UNIONFS)                 \
    || defined(CONFIG_FS_HOSTFS)  || defined(CONFIG_FS_RAMFS)
#define NODFS_SUPPORT
#endif

/****************************************************************************
 * Private Types
 ****************************************************************************/

struct fsmap_t
{
    const char                      *fs_filesystemtype;
    const struct mountpt_operations *fs_mops;
};

/****************************************************************************
 * Private Data
 ****************************************************************************/

#ifdef BDFS_SUPPORT
/* File systems that require block drivers */

#ifdef CONFIG_FS_FAT
extern const struct mountpt_operations fat_operations;
#endif
#ifdef CONFIG_FS_ROMFS
extern const struct mountpt_operations romfs_operations;
#endif
#ifdef CONFIG_FS_SMARTFS
extern const struct mountpt_operations smartfs_operations;
#endif
#ifdef CONFIG_FS_LITTLEFS
extern const struct mountpt_operations littlefs_operations;
#endif
#ifdef CONFIG_FS_LWEXT4
extern const struct mountpt_operations g_lwext4_operations;
#endif

static const struct fsmap_t g_bdfsmap[] = {
#ifdef CONFIG_FS_FAT
    {    "vfat",      &fat_operations},
#endif
#ifdef CONFIG_FS_ROMFS
    {   "romfs",    &romfs_operations},
#endif
#ifdef CONFIG_FS_SMARTFS
    { "smartfs",  &smartfs_operations},
#endif
#ifdef CONFIG_FS_LITTLEFS
    {"littlefs", &littlefs_operations},
#endif
#ifdef CONFIG_FS_LWEXT4
    {  "ext4", &g_lwext4_operations},
#endif
    {      NULL,                 NULL},
};
#endif /* BDFS_SUPPORT */

#ifdef MDFS_SUPPORT
/* File systems that require MTD drivers */

#ifdef CONFIG_FS_ROMFS
extern const struct mountpt_operations romfs_operations;
#endif
#ifdef CONFIG_FS_SPIFFS
extern const struct mountpt_operations spiffs_operations;
#endif
#ifdef CONFIG_FS_LITTLEFS
extern const struct mountpt_operations littlefs_operations;
#endif

static const struct fsmap_t g_mdfsmap[] = {
#ifdef CONFIG_FS_ROMFS
    {   "romfs",    &romfs_operations},
#endif
#ifdef CONFIG_FS_SPIFFS
    {  "spiffs",   &spiffs_operations},
#endif
#ifdef CONFIG_FS_LITTLEFS
    {"littlefs", &littlefs_operations},
#endif
#ifdef CONFIG_FS_LWEXT4
    {  "ext4", &g_lwext4_operations},
#endif
    {      NULL,                 NULL},
};
#endif /* MDFS_SUPPORT */

#ifdef NODFS_SUPPORT
/* File systems that require neither block nor MTD drivers */
#ifdef CONFIG_FS_RAMFS
extern const struct mountpt_operations ramfs_operations;
#endif
#ifdef CONFIG_FS_TMPFS
extern const struct mountpt_operations tmpfs_operations;
#endif
#ifdef CONFIG_FS_SHMFS
extern const struct mountpt_operations shmfs_operations;
#endif
#ifdef CONFIG_NFS
extern const struct mountpt_operations nfs_operations;
#endif
#ifdef CONFIG_FS_PROCFS
extern const struct mountpt_operations procfs_operations;
#endif
#ifdef CONFIG_FS_USERFS
extern const struct mountpt_operations userfs_operations;
#endif
#ifdef CONFIG_FS_HOSTFS
extern const struct mountpt_operations hostfs_operations;
#endif
#ifdef CONFIG_FS_CROMFS
extern const struct mountpt_operations cromfs_operations;
#endif
#ifdef CONFIG_FS_UNIONFS
extern const struct mountpt_operations unionfs_operations;
#endif
#ifdef CONFIG_FS_RPMSGFS
extern const struct mountpt_operations rpmsgfs_operations;
#endif

static const struct fsmap_t g_nonbdfsmap[] = {
#ifdef CONFIG_FS_RAMFS
    {"ramfs", &ramfs_operations},
#endif
#ifdef CONFIG_FS_TMPFS
    {  "tmpfs",   &tmpfs_operations},
#endif
#ifdef CONFIG_FS_SHMFS
    {  "shmfs",   &shmfs_operations},
#endif
#ifdef CONFIG_NFS
    {    "nfs",     &nfs_operations},
#endif
#ifdef CONFIG_FS_PROCFS
    { "proc",  &procfs_operations},
#endif
#ifdef CONFIG_FS_USERFS
    { "userfs",  &userfs_operations},
#endif
#ifdef CONFIG_FS_HOSTFS
    { "hostfs",  &hostfs_operations},
#endif
#ifdef CONFIG_FS_CROMFS
    { "cromfs",  &cromfs_operations},
#endif
#ifdef CONFIG_FS_UNIONFS
    {"unionfs", &unionfs_operations},
#endif
#ifdef CONFIG_FS_RPMSGFS
    {"rpmsgfs", &rpmsgfs_operations},
#endif
    {     NULL,                NULL},
};
#endif /* NODFS_SUPPORT */

/****************************************************************************
 * Private Functions
 ****************************************************************************/

/****************************************************************************
 * Name: mount_findfs
 *
 * Description:
 *    find the specified filesystem
 *
 ****************************************************************************/

#if defined(BDFS_SUPPORT) || defined(MDFS_SUPPORT) || defined(NODFS_SUPPORT)
static const struct mountpt_operations *
mount_findfs (const struct fsmap_t *fstab, const char *filesystemtype)
{
    const struct fsmap_t *fsmap;

    for (fsmap = fstab; fsmap->fs_filesystemtype; fsmap++)
    {
        if (strcmp (filesystemtype, fsmap->fs_filesystemtype) == 0)
        {
            return fsmap->fs_mops;
        }
    }

    return NULL;
}
#endif

/****************************************************************************
 * Public Functions
 ****************************************************************************/

void foreach_nonbd_filesystems(void (*func) (const char *filesystemtype, void *arg), void *arg)
{
    const struct fsmap_t *fsmap;
    for (fsmap = g_nonbdfsmap; fsmap->fs_filesystemtype; fsmap++)
    {
        func(fsmap->fs_filesystemtype, arg);
    }
}

void foreach_bd_filesystems(void (*func) (const char *filesystemtype, void *arg), void *arg)
{
    const struct fsmap_t *fsmap;
    for (fsmap = g_bdfsmap; fsmap->fs_filesystemtype; fsmap++)
    {
        func(fsmap->fs_filesystemtype, arg);
    }
}

/****************************************************************************
 * Name: vfs_mount
 *
 * Description:
 *   vfs_mount() is similar to the standard 'mount' interface except that is
 *   not a cancellation point and it does not modify the errno variable.
 *
 *   vfs_mount() is an internal NuttX interface and should not be called from
 *   applications.
 *
 * Returned Value:
 *   Zero is returned on success; a negated value is returned on any failure.
 *
 ****************************************************************************/
static int nfs_client_init(char *source, struct nfs_args *arg)
{
    int ret = 0;

    struct sockaddr_in *sin = NULL;

    if(arg == NULL)
    {
        KLOG_E("nfs_client_init failed");
        return -1;
    }

    char *nfs_server_ip = strtok((char *)source, ":");

    memset(arg, 0, sizeof(struct nfs_args));
    sin = (struct sockaddr_in *)&(arg->addr);
    ret = inet_pton(AF_INET, nfs_server_ip, &sin->sin_addr);
    if(ret == 1)
    {
        sin->sin_family = AF_INET;
        sin->sin_port   = htons(111);
        arg->addrlen    = sizeof(struct sockaddr_in);
    }
    arg->sotype = SOCK_DGRAM;
    arg->path = strtok(NULL, "");
    arg->flags = 0;       /* 0=Use all defaults */

    return 0;
}

#include <fs/ioctl.h>
#include <fcntl.h>

static int do_mount (const char *source, const char *target,
                     const char *filesystemtype, unsigned long mountflags,
                     const void *data)
{
#if defined(BDFS_SUPPORT) || defined(MDFS_SUPPORT) || defined(NODFS_SUPPORT)
    struct inode                    *drvr_inode = NULL;
    struct inode                    *mountpt_inode;
    const struct mountpt_operations *mops = NULL;
#ifndef CONFIG_DISABLE_PSEUDOFS_OPERATIONS
    struct inode_search_s desc;
#endif
    void *fshandle;
    int   ret;

    /* Verify required pointer arguments */

    assert (target && filesystemtype);

    /* Find the specified filesystem. Try the block driver filesystems first */

    /* Special-case: ext4 等块设备型文件系统，如果 source 是 /dev/mtd*，先通过 mtdproxy 包装成块设备 */
#if defined(CONFIG_FS_LWEXT4) && defined(CONFIG_MTD)
    if (source != NULL && strcmp(filesystemtype, "ext4") == 0 && strncmp(source, "/dev/mtd", 8) == 0)
    {
        char blkbuf[32];
        extern int mtdproxy_prepare_blockdev_path(const char *mtddev, int mountflags, char *out_path, size_t out_sz);
        KLOG_I("fs_mount: wrap %s for fs=%s", source, filesystemtype);
        ret = mtdproxy_prepare_blockdev_path(source, mountflags, blkbuf, sizeof(blkbuf));
        KLOG_I("fs_mount: mtdproxy ret=%d blk=%s", ret, (ret==0)?blkbuf:"<err>");
        if (ret < 0)
        {
            KLOG_E("ERROR: MTD proxy to block for %s failed: %d", source, ret);
            goto errout;
        }
        /* -o autoformat 时，先对临时块设备做一次低级格式化（全盘擦除），避免 Dhara MAP_FULL */
        if (data && strstr(data, "forceformat") != NULL)
        {
            struct file f;
            int oret = block_proxy(&f, blkbuf, O_RDWR);
            if (oret >= 0)
            {
                int ioret = file_ioctl(&f, BIOC_LLFORMAT, 0);
                KLOG_I("fs_mount: BIOC_LLFORMAT ret=%d", ioret);
                file_close(&f);
            }
            else
            {
                KLOG_W("fs_mount: block_proxy(%s) ret=%d, skip LLFORMAT", blkbuf, oret);
            }
        }
        /* 用生成的临时块设备替换 source 并继续后续块设备挂载分支 */
        source = strdup(blkbuf);
    }
#endif

    if (source != NULL
        && find_blockdriver (source, mountflags, &drvr_inode) >= 0)
    {
        /* Find the block based file system */

#ifdef BDFS_SUPPORT
        mops = mount_findfs (g_bdfsmap, filesystemtype);
#endif /* BDFS_SUPPORT */
        if (mops == NULL)
        {
            KLOG_E ("ERROR: Failed to find block based file system %s",
                  filesystemtype);

            ret = -ENODEV;
            goto errout_with_inode;
        }
    }
    else if (source != NULL
             && find_mtddriver (source, &drvr_inode) >= 0)
    {
        /* Find the MTD based file system */

#ifdef MDFS_SUPPORT
        mops = mount_findfs (g_mdfsmap, filesystemtype);
#endif /* MDFS_SUPPORT */
        if (mops == NULL)
        {
            KLOG_E ("ERROR: Failed to find MTD based file system %s",
                  filesystemtype);

            ret = -ENODEV;
            goto errout_with_inode;
        }
    }
    else
#ifdef NODFS_SUPPORT
        if ((mops = mount_findfs (g_nonbdfsmap, filesystemtype)) != NULL)
    {}
    else
#endif /* NODFS_SUPPORT */
    {
        KLOG_E ("ERROR: Failed to find block driver %s", source);

        ret = -ENOTBLK;
        goto errout;
    }

    ret = inode_lock ();
    if (ret < 0)
    {
        goto errout_with_inode;
    }

#ifndef CONFIG_DISABLE_PSEUDOFS_OPERATIONS
    /* Check if the inode already exists */

    SETUP_SEARCH (&desc, target, false);

    ret = inode_find (&desc);
    if (ret >= 0)
    {
        /* Successfully found.  The reference count on the inode has been
         * incremented.
         */

        mountpt_inode = desc.node;
        assert (mountpt_inode != NULL);

        /* But is it a directory node (i.e., not a driver or other special
         * node)?
         */

        if (!INODE_IS_PSEUDODIR (mountpt_inode))
        {
            KLOG_E ("ERROR: target %s exists and is a special node", target);
            ret = -ENOTDIR;
            inode_release (mountpt_inode);
            goto errout_with_lock;
        }
    }
    else
#endif

    /* Insert a dummy node -- we need to hold the inode semaphore
     * to do this because we will have a momentarily bad structure.
     * NOTE that the new inode will be created with an initial reference
     * count of zero.
     */

    {
        ret = inode_reserve (target, 0777, &mountpt_inode);
        if (ret < 0)
        {
            /* inode_reserve can fail for a couple of reasons, but the most
             * likely one is that the inode already exists. inode_reserve may
             * return:
             *
             *  -EINVAL - 'path' is invalid for this operation
             *  -EEXIST - An inode already exists at 'path'
             *  -ENOMEM - Failed to allocate in-memory resources for the
             *            operation
             */

            KLOG_E ("ERROR: Failed to reserve inode for target %s", target);
            goto errout_with_lock;
        }
    }

    /* Bind the block driver to an instance of the file system.  The file
     * system returns a reference to some opaque, fs-dependent structure
     * that encapsulates this binding.
     */

    if (mops->bind == NULL)
    {
        /* The filesystem does not support the bind operation ??? */

        KLOG_E ("ERROR: Filesystem does not support bind");
        ret = -EINVAL;
        goto errout_with_mountpt;
    }

    /* Increment reference count for the reference we pass to the file system */

#if defined(BDFS_SUPPORT) || defined(MDFS_SUPPORT)
#ifdef NODFS_SUPPORT
    if (drvr_inode != NULL)
#endif
    {
        drvr_inode->i_crefs++;
    }
#endif

    /* On failure, the bind method returns -errorcode */

#if defined(BDFS_SUPPORT) || defined(MDFS_SUPPORT)
    struct nfs_args data_arg;

    if (strcmp(filesystemtype, "nfs") == 0)
    {
        nfs_client_init((char *)source, &data_arg);
        ret = mops->bind (drvr_inode, &data_arg, &fshandle);
    }
    else
    {
        ret = mops->bind (drvr_inode, data, &fshandle);
    }
#else
    ret = mops->bind (NULL, data, &fshandle);
#endif
    if (ret < 0)
    {
        /* The inode is unhappy with the driver for some reason.  Back out
         * the count for the reference we failed to pass and exit with an
         * error.
         */

        KLOG_E ("ERROR: Bind method failed: %d", ret);

#if defined(BDFS_SUPPORT) || defined(MDFS_SUPPORT)
#ifdef NODFS_SUPPORT
        if (drvr_inode != NULL)
#endif
        {
            drvr_inode->i_crefs--;
        }
#endif

        goto errout_with_mountpt;
    }

    /* We have it, now populate it with driver specific information. */

    INODE_SET_MOUNTPT (mountpt_inode);

    mountpt_inode->u.i_mops  = mops;
    mountpt_inode->i_private = fshandle;
    inode_unlock ();

    /* We can release our reference to the blkdrver_inode, if the filesystem
     * wants to retain the blockdriver inode (which it should), then it must
     * have called inode_addref().  There is one reference on mountpt_inode
     * that will persist until umount2() is called.
     */

#if defined(BDFS_SUPPORT) || defined(MDFS_SUPPORT)
#ifdef NODFS_SUPPORT
    if (drvr_inode != NULL)
#endif
    {
        inode_release (drvr_inode);
    }
#endif

#ifndef CONFIG_DISABLE_PSEUDOFS_OPERATIONS
    RELEASE_SEARCH (&desc);
#endif
    return 0;

    /* A lot of goto's!  But they make the error handling much simpler */

errout_with_mountpt:
    inode_release (mountpt_inode);
    inode_remove (target);

errout_with_lock:
    inode_unlock ();
#ifndef CONFIG_DISABLE_PSEUDOFS_OPERATIONS
    RELEASE_SEARCH (&desc);
#endif

errout_with_inode:
#if defined(BDFS_SUPPORT) || defined(MDFS_SUPPORT)
    if (drvr_inode != NULL)
    {
        inode_release (drvr_inode);
    }
#endif

errout:
    return ret;

#else
    KLOG_E ("ERROR: No filesystems enabled");
    return -ENOSYS;
#endif /* BDFS_SUPPORT || MDFS_SUPPORT || NODFS_SUPPORT */
}

struct mount_point
{
    struct list_head list;
    char             real_path[256];
    char            *path;
    char * dev_path;
    char            *fs_type;
    int              path_len;
    unsigned long mountflags;
};

static LIST_HEAD (g_mount_list);
static int g_mount_num;

struct mount_point *mount_point_create (const char *mount_path, const char * source, const char * fstype, unsigned long mountflags)
{
    struct mount_point *mp;
    mp = malloc (sizeof *mp);
    snprintf (mp->real_path, sizeof (mp->real_path), "/sys/mount/%d",
              g_mount_num++);
    mp->mountflags = mountflags;
    mp->path     = strdup (mount_path);
    mp->path_len = strlen (mp->path);
    if(source)
    {
        mp->dev_path     = strdup (source);
    }
    else
    {
        mp->dev_path     = strdup (fstype);
    }
    mp->fs_type = strdup (fstype);
    list_add (&mp->list, &g_mount_list);
    return mp;
}

void foreach_mount_point(void (*func) (const char *mount_path, const char * source, const char * fstype, unsigned long mountflags, void*arg), void*arg)
{
    struct mount_point *pos;
    list_for_each_entry (pos, &g_mount_list, list)
    {
        func(pos->path, pos->dev_path, pos->fs_type, pos->mountflags, arg);
    }
}

char *mount_point_get_real_path (const char *path)
{
    struct mount_point *pos;
    struct mount_point *found    = NULL;
    char               *fix_path = NULL;
    struct mount_point  dev_mp
        = { .path = "/dev", .path_len = strlen ("/dev"), .real_path = "/dev" };

    if (strncmp (path, "/sys/mount", sizeof ("/sys/mount") - 1) == 0)
    {
        return NULL;
    }

    if (strncmp (path, dev_mp.path, dev_mp.path_len) == 0)
    {
        if (path[dev_mp.path_len] == '\0' || path[dev_mp.path_len] == '/')
        {
            found = &dev_mp;
        }
    }

    list_for_each_entry (pos, &g_mount_list, list)
    {
        if (strncmp (pos->path, path, pos->path_len) == 0)
        {
            if (found)
            {
                if (found->path_len < pos->path_len && (path[pos->path_len] == '\0' || path[pos->path_len] == '/'))
                {
                    found = pos;
                }
            }
            else
            {
                found = pos;
            }
        }
    }

    if (found)
    {
        asprintf (&fix_path, "%s/%s", found->real_path, &path[found->path_len]);
    }

    return fix_path;
}

char *simplifyPath (const char *path);
char *mount_point_get_source_path (const char *path)
{
    struct mount_point *pos;
    struct mount_point *found    = NULL;
    char               *fix_path = NULL;
    if (strncmp (path, "/sys/mount", sizeof ("/sys/mount") - 1) != 0)
    {
        return strdup(path);
    }
    list_for_each_entry (pos, &g_mount_list, list)
    {
        if (strncmp (pos->real_path, path, strlen(pos->real_path)) == 0)
        {
            found = pos;
            break;
        }
    }
    if (found)
    {
        asprintf (&fix_path, "%s/%s", found->path, &path[strlen(pos->real_path)]);
    }
    if(fix_path == NULL)
    {
        fix_path = strdup(path);
    }
    char *simp_path = simplifyPath(fix_path);
    free(fix_path);
    return simp_path;
}

void mount_point_destroy (struct mount_point *mp)
{
    list_del(&mp->list);
    free(mp->dev_path);
    free(mp->fs_type);
    free (mp->path);
    free (mp);
}

void mount_point_destroy_by_path (const char * path)
{
    struct mount_point *mp;
    list_for_each_entry (mp, &g_mount_list, list)
    {
        if (strcmp (mp->path, path) == 0)
        {
            mount_point_destroy(mp);
            return;
        }
    }
}

int vfs_mount (const char *source, const char *target,
               const char *filesystemtype, unsigned long mountflags,
               const void *data)
{
    struct mount_point *mp;
    int                 ret;
    bool                link_dev = false;
    if (strncmp (target, "/dev", strlen ("/dev")) != 0)
    {
        if (vfs_access (target, F_OK) != 0)
        {
            return -ENOENT;
        }
    }

    mp  = mount_point_create (target, source, filesystemtype, mountflags);
    ret = do_mount (source, mp->real_path, filesystemtype, mountflags, data);
    if (ret < 0)
    {
        mount_point_destroy (mp);
        return ret;
    }

    return ret;
}
