#include <arch/sys_arch.h>
#include <lwip/netif.h>
#include <lwip/tcpip.h>
#include <net/ethernet_dev.h>
#include <net/netdev.h>
#include <ttos_init.h>

#define KLOG_TAG "LWIP_INIT"
#include <klog.h>

typedef struct T_TTOS_TaskControlBlock_Struct *TASK_ID;

extern const struct netstack_ops lwip_netdev_ops;
extern struct netstack_ops *eth_stack_netdev_ops;
extern netdev_install_cb eth_netdev_install_cb;
extern void lwip_register_sockif();
extern int eth_lwip_netdev_install_callback();
extern void eth_device_loopback_init();
extern void eth_task_defer_config();

static void tcpip_init_done_callback()
{
    KLOG_D("LWIP init done");

    /* lwip tcpip启动后挂载psock */
    lwip_register_sockif();

    eth_device_loopback_init ();
}

int ttos_lwip_init()
{
#if defined(CONFIG_LWIP_TCPIP_THREAD_BIND_CPU_CORE) || defined(CONFIG_ETH_RX_TASK_BIND_CPU_CORE)
    char task_name[32];
    TASK_ID task_id;
    int ret;
#endif

    KLOG_D("LWIP init start");

    eth_netdev_install_cb = eth_lwip_netdev_install_callback;

    eth_stack_netdev_ops = (struct netstack_ops *)&lwip_netdev_ops;

    tcpip_init(tcpip_init_done_callback, NULL);

#if defined(CONFIG_LWIP_TCPIP_THREAD_BIND_CPU_CORE) || defined(CONFIG_ETH_RX_TASK_BIND_CPU_CORE)
    snprintf(task_name, sizeof(task_name), "eth_defer_cfg");
    ret = (int)TTOS_CreateTaskEx((unsigned char *)&task_name, CONFIG_ETH_RX_TASK_PRIORITY - 1,
                                 TRUE, TRUE, (void (*)())eth_task_defer_config, NULL,
                                 DEFAULT_TASK_STACK_SIZE, &task_id);
#endif

    return 0;
}
