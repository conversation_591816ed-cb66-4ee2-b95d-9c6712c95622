#include "dhara/dhara/map.h"
#include "dhara/dhara/nand.h"
#include <errno.h>
#include <fs/fs.h>
#include <mtd/mtd.h>
#include <stddef.h>
#include <stdlib.h>
#include <string.h>
#define KLOG_TAG "dhara"
#include <klog.h>

struct dhara_glue
{
    struct mtd_dev_s *mtd;
    struct dhara_map map;
    struct dhara_nand nand;
    unsigned p_bits; /* log2(page_size) */
    unsigned b_bits; /* log2(block_pages) */
    uint8_t *page_buf;
};

int dhara_glue_read(const struct dhara_nand *n, dhara_page_t page, size_t offset, size_t length,
                    uint8_t *data, dhara_error_t *err)
{
    (void)err;
    struct dhara_glue *g = (struct dhara_glue *)((char *)n - offsetof(struct dhara_glue, nand));
    size_t pagesz = 1u << g->p_bits;
    off_t byte_off = (off_t)page * pagesz + (off_t)offset;
    ssize_t r = MTD_READ(g->mtd, byte_off, length, data);
    // KLOG_I("dhara_nand_read: page=%lu off=%u len=%u -> r=%ld", (unsigned long)page,
    // (unsigned)offset, (unsigned)length, (long)r);
    return (r < 0) ? -1 : 0;
}

int dhara_glue_prog(const struct dhara_nand *n, dhara_page_t page, const uint8_t *data,
                    dhara_error_t *err)
{
    (void)err;
    struct dhara_glue *g = (struct dhara_glue *)((char *)n - offsetof(struct dhara_glue, nand));
    ssize_t w = MTD_BWRITE(g->mtd, (off_t)page, 1, data);
    // KLOG_I("dhara_nand_prog: page=%lu -> w=%ld", (unsigned long)page, (long)w);
    return (w == 1) ? 0 : -1;
}

int dhara_glue_erase(const struct dhara_nand *n, dhara_block_t b, dhara_error_t *err)
{
    (void)err;
    struct dhara_glue *g = (struct dhara_glue *)((char *)n - offsetof(struct dhara_glue, nand));
    int r = MTD_ERASE(g->mtd, (off_t)b, 1);
    // KLOG_I("dhara_nand_erase: block=%lu -> r=%d", (unsigned long)b, r);
    return (r < 0) ? -1 : 0;
}

int dhara_glue_is_bad(const struct dhara_nand *n, dhara_block_t b)
{
    struct dhara_glue *g = (struct dhara_glue *)((char *)n - offsetof(struct dhara_glue, nand));
    if (!g->mtd->isbad)
        return 0;
    int r = g->mtd->isbad(g->mtd, (off_t)b);
    return r > 0 ? 1 : 0; /* treat errors/non-positive as not-bad */
}

int dhara_glue_is_free(const struct dhara_nand *n, dhara_page_t p)
{
    struct dhara_glue *g = (struct dhara_glue *)((char *)n - offsetof(struct dhara_glue, nand));
    /* Check if page is all 0xFF */
    size_t pagesz = 1u << g->p_bits;
    uint8_t *buf = g->page_buf;
    if (!buf)
        return 0;
    if (MTD_READ(g->mtd, (off_t)p * pagesz, pagesz, buf) < 0)
        return 0;
    for (size_t i = 0; i < pagesz; i++)
        if (buf[i] != 0xFF)
            return 0;
    return 1;
}

static int dhara_blk_open(struct inode *inode)
{
    (void)inode;
    return 0;
}
static int dhara_blk_close(struct inode *inode)
{
    (void)inode;
    return 0;
}

static ssize_t dhara_blk_read(struct inode *inode, unsigned char *buffer, blkcnt_t start_sector,
                              unsigned int nsectors)
{
    struct dhara_glue *g = (struct dhara_glue *)inode->i_private;
    if (!g)
        return -ENODEV;
    dhara_error_t err = 0;
    size_t pagesz = 1u << g->p_bits;
    uint8_t *dst = buffer;
    for (unsigned int i = 0; i < nsectors; i++)
    {
        int dr = dhara_map_read(&g->map, (dhara_sector_t)(start_sector + i), dst, &err);
        // KLOG_I("dhara_blk_read: lba=%lu -> dr=%d err=%d", (unsigned long)(start_sector + i), dr,
        // (int)err);
        if (dr < 0)
        {
            return -EIO;
        }
        dst += pagesz;
    }
    return (ssize_t)nsectors;
}

static ssize_t dhara_blk_write(struct inode *inode, const unsigned char *buffer,
                               blkcnt_t start_sector, unsigned int nsectors)
{
    struct dhara_glue *g = (struct dhara_glue *)inode->i_private;
    if (!g)
        return -ENODEV;
    dhara_error_t err = 0;
    size_t pagesz = 1u << g->p_bits;
    const uint8_t *src = buffer;
    for (unsigned int i = 0; i < nsectors; i++)
    {
        int dw = dhara_map_write(&g->map, (dhara_sector_t)(start_sector + i), src, &err);
        // KLOG_I("dhara_blk_write: lba=%lu -> dw=%d err=%d", (unsigned long)(start_sector + i), dw,
        // (int)err);
        if (dw < 0)
        {
            return -EIO;
        }
        src += pagesz;
    }
    /* Ensure durability */
    int ds = dhara_map_sync(&g->map, &err);
    // KLOG_I("dhara_blk_sync: ds=%d err=%d", ds, (int)err);
    if (ds < 0)
        return -EIO;
    return (ssize_t)nsectors;
}

static int dhara_blk_geometry(struct inode *inode, struct geometry *geo)
{
    struct dhara_glue *g = (struct dhara_glue *)inode->i_private;
    if (!g)
        return -ENODEV;
    size_t pagesz = 1u << g->p_bits;
    /* capacity = blocks * pages_per_block */
    struct mtd_geometry_s mgeo;
    if (MTD_IOCTL(g->mtd, MTDIOC_GEOMETRY, (unsigned long)&mgeo) < 0)
        return -EIO;
    geo->geo_available = true;
    geo->geo_mediachanged = false;
    geo->geo_writeenabled = true;
    geo->geo_sectorsize = (uint32_t)mgeo.blocksize;
    geo->geo_nsectors = (blkcnt_t)dhara_map_capacity(&g->map);
    return 0;
}

static int dhara_blk_ioctl(struct inode *inode, int cmd, unsigned long arg)
{
    struct dhara_glue *g = (struct dhara_glue *)inode->i_private;
    if (!g)
        return -ENODEV;
    if (cmd == BIOC_FLUSH)
    {
        dhara_error_t err = 0;
        return dhara_map_sync(&g->map, &err) < 0 ? -EIO : 0;
    }
    else if (cmd == BIOC_LLFORMAT)
    {
        /* Full erase (low-level format) of the underlying MTD, skipping bad blocks */
        struct mtd_geometry_s mgeo;
        int ret = MTD_IOCTL(g->mtd, MTDIOC_GEOMETRY, (unsigned long)&mgeo);
        if (ret < 0)
            return ret;
        for (off_t b = 0; b < (off_t)mgeo.neraseblocks; ++b)
        {
            int bad = g->mtd->isbad ? g->mtd->isbad(g->mtd, b) : 0;
            if (bad > 0)
                continue;
            ret = MTD_ERASE(g->mtd, b, 1);
            if (ret < 0)
                return ret;
        }
        /* Reset dhara map after erase */
        dhara_map_clear(&g->map);
        KLOG_I("dhara: low-level format done (erased %u blocks)", mgeo.neraseblocks);
        return 0;
    }
    else if (cmd == MTDIOC_GEOMETRY)
    {
        /* Provide geometry in a way lwext4 expects when talking to a block device:
           - blocksize: NAND page size
           - neraseblocks: total number of pages (so that part_size = blocksize * neraseblocks) */
        struct mtd_geometry_s ugeo;
        int ret = MTD_IOCTL(g->mtd, MTDIOC_GEOMETRY, (unsigned long)&ugeo);
        if (ret < 0)
            return ret;
        struct mtd_geometry_s *out = (struct mtd_geometry_s *)arg;
        if (!out)
            return -EINVAL;
        // size_t pagesz = (size_t)(1u << g->p_bits);
        out->blocksize = (uint32_t)ugeo.blocksize;
        /* Keep real erasesize for completeness; ext4 path won’t use it for capacity */
        out->erasesize = ugeo.erasesize;
        out->neraseblocks =
            (uint32_t)dhara_map_capacity(&g->map) / (out->erasesize / out->blocksize);
        return 0;
    }
    return -ENOTTY;
}

static const struct block_operations g_dhara_bops = {
    .open = dhara_blk_open,
    .close = dhara_blk_close,
    .read = dhara_blk_read,
    .write = dhara_blk_write,
    .geometry = dhara_blk_geometry,
    .ioctl = dhara_blk_ioctl,
};

int dhara_initialize_by_path(const char *path, struct mtd_dev_s *mtd)
{
    if (!path || !mtd)
        return -EINVAL;

    struct mtd_geometry_s geo;
    int ret = MTD_IOCTL(mtd, MTDIOC_GEOMETRY, (unsigned long)&geo);
    if (ret < 0)
        return ret;

    /* Expect power-of-two sizes */
    unsigned p_bits = 0, b_bits = 0;
    {
        uint32_t pg = geo.blocksize;  /* page size */
        uint32_t blk = geo.erasesize; /* bytes per erase block */
        while ((1u << p_bits) < pg)
            p_bits++;
        uint32_t pages_per_blk = blk / pg;
        while ((1u << b_bits) < pages_per_blk)
            b_bits++;
    }

    struct dhara_glue *g = (struct dhara_glue *)malloc(sizeof(*g));
    if (!g)
        return -ENOMEM;
    memset(g, 0, sizeof(*g));
    g->mtd = mtd;
    g->p_bits = p_bits;
    g->b_bits = b_bits;
    g->page_buf = (uint8_t *)malloc(geo.blocksize);
    if (!g->page_buf)
    {
        free(g);
        return -ENOMEM;
    }

    g->nand.log2_page_size = p_bits;
    g->nand.log2_ppb = b_bits;
    g->nand.num_blocks = geo.neraseblocks;

    /* Initialize map */
    dhara_map_init(&g->map, &g->nand, g->page_buf, 1);

    dhara_error_t err = 0;
    if (dhara_map_resume(&g->map, &err) < 0)
    {
        /* No existing state: start empty */
        dhara_map_clear(&g->map);
        KLOG_I("dhara: no existing map, start empty");
    }
    else
    {
        KLOG_I("dhara: map resume ok");
    }

    /* Register block driver at path */
    return register_blockdriver(path, &g_dhara_bops, 0660, g);
}
