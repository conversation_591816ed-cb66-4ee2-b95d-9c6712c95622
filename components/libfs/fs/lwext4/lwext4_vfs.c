/****************************************************************************
 * fs/lwext4/lfs_vfs.c
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

/****************************************************************************
 * Included Files
 ****************************************************************************/

#include <sys/stat.h>
#include <sys/statfs.h>
#include <sys/types.h>

#include <linux/macros.h>
#include <stddef.h>

#include <assert.h>
#include <errno.h>
#include <fcntl.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <ttos.h>
#include <unistd.h>

#include <fs/fs.h>
#include <fs/ioctl.h>
#include <mtd/mtd.h>

#include "../inode/inode.h"
#include "libgen.h"

#include <ext4.h>
#include <ext4_blockdev.h>
#include <ext4_errno.h>
#include <ext4_inode.h>
#include <ext4_mkfs.h>

/****************************************************************************
 * Pre-processor Definitions
 ****************************************************************************/

/****************************************************************************
 * Private Types
 ****************************************************************************/

struct lwext4_dir_s
{
    struct fs_dirent_s base;
    ext4_dir           dir;
};

struct lwext4_file_s
{
    ext4_file file;
    char     *file_path;
    int       refs;
};

/* This structure represents the overall mountpoint state. An instance of
 * this structure is retained as inode private data on each mountpoint that
 * is mounted with a lwext4 filesystem.
 */

struct lwext4_mountpt_s
{
    char                  mount_path[PATH_MAX];
    struct inode         *drv;
    struct mtd_geometry_s geo;
    struct ext4_blockdev  blkdev;
    MUTEX_ID              blk_lock;
};

/****************************************************************************
 * Private Function Prototypes
 ****************************************************************************/

static int     lwext4_open (struct file *filep, const char *relpath, int oflags,
                            mode_t mode);
static int     lwext4_close (struct file *filep);
static ssize_t lwext4_read (struct file *filep, char *buffer, size_t buflen);
static ssize_t lwext4_write (struct file *filep, const char *buffer,
                             size_t buflen);
static off_t   lwext4_seek (struct file *filep, off_t offset, int whence);
static int     lwext4_ioctl (struct file *filep, int cmd, unsigned long arg);
static int     lwext4_sync (struct file *filep);
static int     lwext4_dup (const struct file *oldp, struct file *newp);
static int     lwext4_fstat (const struct file *filep, struct stat *buf);
static int     lwext4_truncate (struct file *filep, off_t length);
static int     lwext4_opendir (struct inode *mountpt, const char *relpath,
                               struct fs_dirent_s **dir);
static int     lwext4_closedir (struct inode *mountpt, struct fs_dirent_s *dir);
static int     lwext4_readdir (struct inode *mountpt, struct fs_dirent_s *dir,
                               struct dirent *entry);
static int lwext4_rewinddir (struct inode *mountpt, struct fs_dirent_s *dir);
static int lwext4_bind (struct inode *driver, const void *data, void **handle);
static int lwext4_unbind (void *handle, struct inode **driver,
                          unsigned int flags);
static int lwext4_statfs (struct inode *mountpt, struct statfs *buf);
static int lwext4_unlink (struct inode *mountpt, const char *relpath);
static int lwext4_mkdir (struct inode *mountpt, const char *relpath,
                         mode_t mode);
static int lwext4_rmdir (struct inode *mountpt, const char *relpath);
static int lwext4_rename (struct inode *mountpt, const char *oldrelpath,
                          const char *newrelpath);
static int lwext4_stat (struct inode *mountpt, const char *relpath,
                        struct stat *buf);
static int lwext4_blkdev_open (struct ext4_blockdev *bdev);
static int lwext4_blkdev_close (struct ext4_blockdev *bdev);
static int lwext4_read_block (struct ext4_blockdev *bdev, void *buf,
                              uint64_t blk_id, uint32_t blk_cnt);
static int lwext4_write_block (struct ext4_blockdev *bdev, const void *buf,
                               uint64_t blk_id, uint32_t blk_cnt);
static int lwext4_sync_block (struct lwext4_mountpt_s *fs);
static int lwext4_chstat (struct inode *mountpt, const char *relpath,
                          const struct stat *buf, int flags);
static int lwext4_fchstat (const struct file *filep, const struct stat *buf,
                           int flags);
static ssize_t lwext4_readlink (struct inode *mountpt, const char *relpath,
                                char *buf, size_t bufsize);
static int     lwext4_symlink (struct inode *mountpt, const char *target,
                               const char *link_relpath);
static int     lwext4_link (struct inode *mountpt, const char *target,
                            const char *link_relpath);
/****************************************************************************
 * Public Data
 ****************************************************************************/

/* See fs_mount.c -- this structure is explicitly extern'ed there.
 * We use the old-fashioned kind of initializers so that this will compile
 * with any compiler.
 */

const struct mountpt_operations g_lwext4_operations = {
    lwext4_open,      /* open */
    lwext4_close,     /* close */
    lwext4_read,      /* read */
    lwext4_write,     /* write */
    lwext4_seek,      /* seek */
    lwext4_ioctl,     /* ioctl */
    NULL,             /* mmap */
    NULL,             /* poll */
    lwext4_sync,      /* sync */
    lwext4_dup,       /* dup */
    lwext4_fstat,     /* fstat */
    lwext4_fchstat,   /* fchstat */
    lwext4_truncate,  /* truncate */
    lwext4_opendir,   /* opendir */
    lwext4_closedir,  /* closedir */
    lwext4_readdir,   /* readdir */
    lwext4_rewinddir, /* rewinddir */
    lwext4_bind,      /* bind */
    lwext4_unbind,    /* unbind */
    lwext4_statfs,    /* statfs */
    lwext4_unlink,    /* unlink */
    lwext4_mkdir,     /* mkdir */
    lwext4_rmdir,     /* rmdir */
    lwext4_rename,    /* rename */
    lwext4_stat,      /* stat */
    lwext4_chstat,    /* chstat */
    lwext4_readlink,  /* readlink */
    lwext4_symlink,   /* symlink */
    lwext4_link,      /* link */
};

static MUTEX_ID         lock;
static struct ext4_lock ext4_lock;
/****************************************************************************
 * Private Functions
 ****************************************************************************/

static void ext4_mutexlock (void)
{
    TTOS_ObtainMutex (lock, TTOS_WAIT_FOREVER);
}

static void ext4_mutexunlock (void)
{
    TTOS_ReleaseMutex (lock);
}

/****************************************************************************
 * Name: lwext4_convert_result
 ****************************************************************************/

static int lwext4_convert_result (int ret)
{
    if (ret > 0)
    {
        return -ret;
    }
    return ret;
}

/****************************************************************************
 * Name: lwext4_convert_oflags
 ****************************************************************************/

static int lwext4_convert_oflags (int oflags)
{
    return oflags;
}

/****************************************************************************
 * Name: lwext4_open
 ****************************************************************************/

static int lwext4_open (struct file *filep, const char *relpath, int oflags,
                        mode_t mode)
{
    struct lwext4_mountpt_s *fs;
    struct lwext4_file_s    *priv;
    struct inode            *inode;
    char                    *fullpath;

    uint32_t old_mode;
    bool file_is_created = false;
    int ret = 0;

    /* Get the mountpoint inode reference from the file structure and the
     * mountpoint private data from the inode structure
     */

    inode = filep->f_inode;
    fs    = inode->i_private;

    asprintf (&fullpath, "%s%s", fs->mount_path, relpath);

    if (fullpath == NULL)
    {
        return -ENOMEM;
    }

    /* Allocate memory for the open file */

    priv = malloc (sizeof (*priv));
    if (priv == NULL)
    {
        free (fullpath);
        return -ENOMEM;
    }
    memset (priv, 0, sizeof (*priv));
    priv->refs      = 1;
    priv->file_path = strdup (relpath);

    /* Lock */
    ext4_mutexlock();

    if((oflags & O_CREAT) && ext4_mode_get(fullpath, &old_mode) != EOK)
    {
        file_is_created = true;
    }

    /* Try to open the file */

    oflags = lwext4_convert_oflags (oflags);
    ret = lwext4_convert_result (ext4_fopen2 (&priv->file, fullpath, oflags));
    if (ret < 0)
    {
        /* Error opening file */
        free (fullpath);
        goto errout;
    }

    /* 创建文件时设置对应的mode */
    if (file_is_created)
    {
        ext4_mode_set (fullpath, mode);
    }

    free (fullpath);

    /* Attach the private date to the struct file instance */

    filep->f_priv = priv;
    ext4_mutexunlock();
    return EOK;

errout_with_file:
    ext4_fclose (&priv->file);
errout:
    if (priv && (priv->file_path != NULL))
        free (priv->file_path);
    free (priv);
    ext4_mutexunlock();
    return ret;
}

/****************************************************************************
 * Name: lwext4_close
 ****************************************************************************/

static int lwext4_close (struct file *filep)
{
    struct lwext4_file_s *priv;
    int                   ret = 0;

    /* Recover our private data from the struct file instance */

    priv = filep->f_priv;

    ext4_mutexlock();

    /* Close the file */

    if (--priv->refs <= 0)
    {
        ret = lwext4_convert_result (ext4_fclose (&priv->file));
    }

    if (priv->refs <= 0)
    {
        free (priv->file_path);
        memset (priv, 0, sizeof (*priv));
        free (priv);
    }

    ext4_mutexunlock();

    return ret;
}

/****************************************************************************
 * Name: lwext4_read
 ****************************************************************************/

static ssize_t lwext4_read (struct file *filep, char *buffer, size_t buflen)
{
    struct lwext4_file_s *priv;
    size_t                rsize;
    ssize_t               ret;

    /* Recover our private data from the struct file instance */

    priv = filep->f_priv;

    ext4_mutexlock();

    /* Call LFS to perform the read */

    if (filep->f_pos != priv->file.fpos)
    {
        ret = lwext4_convert_result (
            ext4_fseek (&priv->file, filep->f_pos, SEEK_SET));
        if (ret < 0)
        {
            goto out;
        }
    }

    ret = lwext4_convert_result (
        ext4_fread (&priv->file, buffer, buflen, &rsize));
    if (ret == EOK)
    {
        filep->f_pos += rsize;
        ret = rsize;
    }

out:
    ext4_mutexunlock();
    return ret;
}

/****************************************************************************
 * Name: lwext4_write
 ****************************************************************************/

static ssize_t lwext4_write (struct file *filep, const char *buffer,
                             size_t buflen)
{
    struct lwext4_file_s *priv;
    ssize_t               ret;
    size_t                wsize;

    /* Recover our private data from the struct file instance */

    priv = filep->f_priv;

    ext4_mutexlock();

    /* Call LFS to perform the write */

    if (filep->f_pos != priv->file.fpos)
    {
        ret = lwext4_convert_result (
            ext4_fseek (&priv->file, filep->f_pos, SEEK_SET));
        if (ret < 0)
        {
            goto out;
        }
    }

    ret = lwext4_convert_result (
        ext4_fwrite (&priv->file, buffer, buflen, &wsize));
    if (ret == EOK)
    {
        filep->f_pos += wsize;
        ret = wsize;
    }

out:
    ext4_mutexunlock();
    return ret;
}

/****************************************************************************
 * Name: lwext4_seek
 ****************************************************************************/

static off_t lwext4_seek (struct file *filep, off_t offset, int whence)
{
    struct lwext4_file_s *priv;
    off_t                 ret;
    off_t                 position;
    ssize_t               oversize;

    /* Recover our private data from the struct file instance */

    priv = filep->f_priv;

    ext4_mutexlock ();

    switch (whence)
    {
    case SEEK_SET: /* The offset is set to offset bytes. */
        position = offset;
        break;

    case SEEK_CUR: /* The offset is set to its current location plus
                    * offset bytes. */
        position = offset + ext4_ftell (&priv->file);
        break;

    case SEEK_END: /* The offset is set to the size of the file plus
                    * offset bytes. */
        position = offset + ext4_fsize (&priv->file);
        break;

    default:
        return -EINVAL;
    }

    oversize = position - (ssize_t)ext4_fsize (&priv->file);

    if (oversize > 0)
    {
        size_t   buf_size = oversize > 1024 ? 1024 : oversize;
        uint8_t *data     = memalign (64, buf_size);
        if (data == NULL)
        {
            ext4_mutexunlock ();
            return -ENOMEM;
        }
        memset (data, 0, buf_size);
        ext4_fseek (&priv->file, 0, SEEK_END);
        while (oversize > 0)
        {
            size_t wsize = oversize > buf_size ? buf_size : oversize;
            ret          = lwext4_convert_result (
                ext4_fwrite (&priv->file, data, wsize, &wsize));
            if (ret < 0)
            {
                free (data);
                ext4_mutexunlock ();
                return ret;
            }
            oversize -= wsize;
        }
        free (data);
    }
    else
    {
        ret = lwext4_convert_result (ext4_fseek (&priv->file, offset, whence));
        if (ret < 0)
        {
            ext4_mutexunlock ();
            return ret;
        }
    }

    /* Call LFS to perform the seek */

    filep->f_pos = position;
    ext4_mutexunlock ();
    return position;
}

/****************************************************************************
 * Name: lwext4_ioctl
 ****************************************************************************/

static int lwext4_ioctl (struct file *filep, int cmd, unsigned long arg)
{
    struct lwext4_mountpt_s *fs;
    struct lwext4_file_s    *priv;

    struct inode *inode;
    struct inode *drv;
    int           ret = 0;

    /* Recover our private data from the struct file instance */

    inode = filep->f_inode;
    fs    = inode->i_private;
    priv  = filep->f_priv;
    drv   = fs->drv;

    if (INODE_IS_MTD (drv))
    {
        return MTD_IOCTL (drv->u.i_mtd, cmd, arg);
    }
    else if (cmd == FIOC_FILEPATH)
    {
        char *ptr = (char *)((uintptr_t)arg);
        ret       = inode_getpath (filep->f_inode, ptr, PATH_MAX);

        if (ret < 0)
        {
            return ret;
        }
        strcat (ptr, priv->file_path);

        return 0;
    }
    else
    {
        if (drv->u.i_bops->ioctl != NULL)
        {
            return drv->u.i_bops->ioctl (drv, cmd, arg);
        }
        else
        {
            return -ENOTTY;
        }
    }
}

/****************************************************************************
 * Name: lwext4_sync
 *
 * Description: Synchronize the file state on disk to match internal, in-
 *   memory state.
 *
 ****************************************************************************/

static int lwext4_sync (struct file *filep)
{
    struct lwext4_mountpt_s *fs;
    struct inode            *inode;
    int                      ret = 0;

    /* Recover our private data from the struct file instance */

    inode = filep->f_inode;
    fs    = inode->i_private;

    ext4_mutexlock();

    ret = lwext4_convert_result (lwext4_sync_block (fs));

    ext4_mutexunlock();

    return ret;
}

/****************************************************************************
 * Name: lwext4_dup
 *
 * Description: Duplicate open file data in the new file structure.
 *
 ****************************************************************************/

static int lwext4_dup (const struct file *oldp, struct file *newp)
{
    struct lwext4_file_s *priv;
    int                   ret = 0;

    /* Recover our private data from the struct file instance */

    priv = oldp->f_priv;

    priv->refs++;
    newp->f_priv = priv;

    return ret;
}

/****************************************************************************
 * Name: lwext4_fstat
 *
 * Description:
 *   Obtain information about an open file associated with the file
 *   descriptor 'fd', and will write it to the area pointed to by 'buf'.
 *
 ****************************************************************************/

static int lwext4_fstat (const struct file *filep, struct stat *buf)
{
    struct lwext4_mountpt_s *fs;
    struct lwext4_file_s    *priv;
    struct inode            *inode;
    struct mtd_geometry_s   *geo;
    int                      ret = 0;
    struct ext4_inode_ref    inode_ref;
    struct ext4_sblock      *sb;

    memset (buf, 0, sizeof (*buf));

    /* Recover our private data from the struct file instance */

    priv  = filep->f_priv;
    inode = filep->f_inode;
    fs    = inode->i_private;
    geo   = &fs->geo;

    ext4_mutexlock();

    /* Call LFS to get file size */

    ret = ext4_get_sblock (fs->mount_path, &sb);
    if (ret < 0)
    {
        goto out;
    }

    /*Load parent*/
    ret = ext4_fs_get_inode_ref (fs->blkdev.fs, priv->file.inode, &inode_ref);
    if (ret < 0)
    {
        goto out;
    }

    /* Convert info to stat */
    buf->st_size    = ext4_fsize (&priv->file);
    buf->st_mode    = ext4_inode_get_mode (sb, inode_ref.inode);
    buf->st_blksize = geo->blocksize;
    buf->st_blocks  = (buf->st_size + buf->st_blksize - 1) / buf->st_blksize;
    buf->st_atim.tv_sec = ext4_inode_get_access_time (inode_ref.inode);
    buf->st_mtim.tv_sec = ext4_inode_get_modif_time (inode_ref.inode);
    buf->st_ctim.tv_sec = ext4_inode_get_change_inode_time (inode_ref.inode);
    buf->st_uid         = ext4_inode_get_uid (inode_ref.inode);
    buf->st_gid         = ext4_inode_get_gid (inode_ref.inode);
    buf->st_dev         = ext4_inode_get_dev (inode_ref.inode);
    buf->st_ino         = priv->file.inode;
    ext4_fs_put_inode_ref (&inode_ref);
out:
    ext4_mutexunlock();
    return ret;
}

/****************************************************************************
 * Name: lwext4_truncate
 *
 * Description:
 *   Set the length of the open, regular file associated with the file
 *   structure 'filep' to 'length'.
 *
 ****************************************************************************/

static int lwext4_truncate (struct file *filep, off_t length)
{
    struct lwext4_file_s *priv;
    int                   ret = 0;

    /* Recover our private data from the struct file instance */

    priv = filep->f_priv;

    ext4_mutexlock();

    /* Call LFS to perform the truncate */

    ret = lwext4_convert_result (ext4_ftruncate (&priv->file, length));

    ext4_mutexunlock();

    return ret;
}

/****************************************************************************
 * Name: lwext4_opendir
 *
 * Description: Open a directory for read access
 *
 ****************************************************************************/

static int lwext4_opendir (struct inode *mountpt, const char *relpath,
                           struct fs_dirent_s **dir)
{
    struct lwext4_mountpt_s *fs;
    struct lwext4_dir_s     *ldir;
    int                      ret = 0;
    char                     fullpath[PATH_MAX];

    /* Recover our private data from the inode instance */

    fs = mountpt->i_private;

    strcpy (fullpath, fs->mount_path);
    strcat (fullpath, relpath);

    /* Allocate memory for the open directory */

    ldir = malloc (sizeof (*ldir));
    if (ldir == NULL)
    {
        return -ENOMEM;
    }

    /* Take the lock */

    ext4_mutexlock();

    /* Call the LFS's opendir function */

    ret = lwext4_convert_result (ext4_dir_open (&ldir->dir, fullpath));
    if (ret < 0)
    {
        goto errout;
    }

    *dir = &ldir->base;
    ext4_mutexunlock();
    return EOK;

errout:
    free (ldir);
    ext4_mutexunlock();
    return ret;
}

/****************************************************************************
 * Name: lwext4_closedir
 *
 * Description: Close a directory
 *
 ****************************************************************************/

static int lwext4_closedir (struct inode *mountpt, struct fs_dirent_s *dir)
{
    struct lwext4_dir_s *ldir;

    /* Recover our private data from the inode instance */

    ldir = (struct lwext4_dir_s *)dir;

    ext4_mutexlock();

    /* Call the LFS's closedir function */

    ext4_dir_close (&ldir->dir);

    ext4_mutexunlock();

    free (ldir);
    return EOK;
}

/****************************************************************************
 * Name: lwext4_readdir
 *
 * Description: Read the next directory entry
 *
 ****************************************************************************/

static int lwext4_readdir (struct inode *mountpt, struct fs_dirent_s *dir,
                           struct dirent *entry)
{
    struct lwext4_dir_s *ldir;
    int                  ret = 0;

    const ext4_direntry *dir_entry;

    /* Recover our private data from the inode instance */

    ldir = (struct lwext4_dir_s *)dir;

    ext4_mutexlock();

    /* Call the LFS's readdir function */

    dir_entry = ext4_dir_entry_next (&ldir->dir);

    if (dir_entry != NULL)
    {
        switch (dir_entry->inode_type)
        {
        case EXT4_DE_DIR:
            entry->d_type = DT_DIR;
            break;
        case EXT4_DE_REG_FILE:
            entry->d_type = DT_REG;
            break;
        case EXT4_DE_CHRDEV:
            entry->d_type = DT_CHR;
            break;
        case EXT4_DE_BLKDEV:
            entry->d_type = DT_BLK;
            break;
        case EXT4_DE_FIFO:
            entry->d_type = DT_FIFO;
            break;
        case EXT4_DE_SOCK:
            entry->d_type = DT_SOCK;
            break;
        case EXT4_DE_SYMLINK:
            entry->d_type = DT_LNK;
            break;
        default:
            entry->d_type = DT_UNKNOWN;
            break;
        }
        strlcpy (entry->d_name, (const char *)dir_entry->name,
                 dir_entry->name_length + 1);
    }
    else if (ret == 0)
    {
        ext4_mutexunlock();
        ret = -ENOENT;
    }
    ext4_mutexunlock();
    return ret;
}

/****************************************************************************
 * Name: lwext4_rewindir
 *
 * Description: Reset directory read to the first entry
 *
 ****************************************************************************/

static int lwext4_rewinddir (struct inode *mountpt, struct fs_dirent_s *dir)
{
    struct lwext4_dir_s *ldir;
    int                  ret = 0;

    /* Recover our private data from the inode instance */

    ldir = (struct lwext4_dir_s *)dir;

    ext4_mutexlock();

    /* Call the LFS's rewinddir function */

    ext4_dir_entry_rewind (&ldir->dir);

    ext4_mutexunlock();

    ret = EOK;

    return ret;
}

/****************************************************************************
 * Name: lwext4_bind
 *
 * Description: This implements a portion of the mount operation. This
 *  function allocates and initializes the mountpoint private data and
 *  binds the driver inode to the filesystem private data. The final
 *  binding of the private data (containing the driver) to the
 *  mountpoint is performed by mount().
 *
 ****************************************************************************/

static int lwext4_blkdev_open (struct ext4_blockdev *bdev)
{
    /* do nothing */

    return EOK;
}

static int lwext4_blkdev_close (struct ext4_blockdev *bdev)
{
    /* do nothing */

    return EOK;
}

static int lwext4_read_block (struct ext4_blockdev *bdev, void *buf,
                              uint64_t blk_id, uint32_t blk_cnt)
{
    struct lwext4_mountpt_s *fs
        = container_of (bdev, struct lwext4_mountpt_s, blkdev);
    struct inode *drv = fs->drv;
    int           ret = 0;

    if (INODE_IS_MTD (drv))
    {
        ret = MTD_BREAD (drv->u.i_mtd, blk_id, blk_cnt, buf);
    }
    else
    {
        ret = drv->u.i_bops->read (drv, buf, blk_id, blk_cnt);
    }

    return ret >= 0 ? EOK : ret;
}

/****************************************************************************
 * Name: lwext4_write_block
 ****************************************************************************/

static int lwext4_write_block (struct ext4_blockdev *bdev, const void *buf,
                               uint64_t blk_id, uint32_t blk_cnt)
{
    struct lwext4_mountpt_s *fs
        = container_of (bdev, struct lwext4_mountpt_s, blkdev);
    struct inode *drv = fs->drv;
    int           ret = 0;

    if (INODE_IS_MTD (drv))
    {
        ret = MTD_ERASE (drv->u.i_mtd, blk_id, blk_cnt);
        if (ret >= 0)
        {
            ret = MTD_BWRITE (drv->u.i_mtd, blk_id, blk_cnt, buf);
        }
    }
    else
    {
        ret = drv->u.i_bops->write (drv, buf, blk_id, blk_cnt);
    }

    return ret >= 0 ? EOK : ret;
}

static int lwext4_sync_block (struct lwext4_mountpt_s *fs)
{
    struct inode *drv = fs->drv;
    int           ret = 0;

    if (INODE_IS_MTD (drv))
    {
        ret = MTD_IOCTL (drv->u.i_mtd, BIOC_FLUSH, 0);
    }
    else
    {
        if (drv->u.i_bops->ioctl != NULL)
        {
            ret = drv->u.i_bops->ioctl (drv, BIOC_FLUSH, 0);
        }
        else
        {
            ret = -ENOTTY;
        }
    }

    return ret == -ENOTTY ? EOK : ret;
}

/**@brief   Lock block device. Required in multi partition mode
 *          operations. Not mandatory field.
 * @param   bdev block device.*/
static int lwext4_blklock (struct ext4_blockdev *bdev)
{
    struct lwext4_mountpt_s *fs = bdev->bdif->p_user;

    T_TTOS_ReturnCode ret = TTOS_ObtainMutex (fs->blk_lock, TTOS_WAIT_FOREVER);
    if (ret != TTOS_OK)
    {
        return -1;
    }
    return 0;
}

/**@brief   Unlock block device. Required in multi partition mode
 *          operations. Not mandatory field.
 * @param   bdev block device.*/
static int lwext4_blkunlock (struct ext4_blockdev *bdev)
{
    struct lwext4_mountpt_s *fs = bdev->bdif->p_user;

    T_TTOS_ReturnCode ret = TTOS_ReleaseMutex (fs->blk_lock);
    if (ret != TTOS_OK)
    {
        return -1;
    }
    return 0;
}
static int g_lwext4_index = 0;
/****************************************************************************
 * Name: lwext4_bind
 ****************************************************************************/

static int lwext4_bind (struct inode *driver, const void *data, void **handle)
{
    struct lwext4_mountpt_s    *fs;
    struct ext4_blockdev_iface *iface;
    int                         ret = 0;
    ext4_dmask_set (DEBUG_ALL);

    /* Open the block driver */

    if (INODE_IS_BLOCK (driver) && driver->u.i_bops->open)
    {
        ret = driver->u.i_bops->open (driver);
        if (ret < 0)
        {
            return ret;
        }
    }

    /* Create an instance of the mountpt state structure */

    fs = calloc (1, sizeof (*fs));
    if (!fs)
    {
        ret = -ENOMEM;
        goto errout_with_block;
    }
    iface = calloc (1, sizeof (*iface));
    if (!iface)
    {
        ret = -ENOMEM;
        goto errout_with_iface;
    }
    fs->blkdev.bdif = iface;

    /* Initialize the allocated mountpt state structure. The filesystem is
     * responsible for one reference on the driver inode and does not
     * have to addref() here (but does have to release in unbind().
     */

    fs->drv = driver; /* Save the driver reference */
    if (INODE_IS_MTD (driver))
    {
        /* Get MTD geometry directly */

        ret = MTD_IOCTL (driver->u.i_mtd, MTDIOC_GEOMETRY,
                         (unsigned long)&fs->geo);
        printk("%s %d\n", __func__, __LINE__);
    }
    else
    {
        /* Try to get FLT MTD geometry first */

        if (driver->u.i_bops->ioctl != NULL)
        {
            ret = driver->u.i_bops->ioctl (driver, MTDIOC_GEOMETRY,
                                           (unsigned long)&fs->geo);
        }
        else
        {
            ret = -ENOTTY;
        }

        if (ret < 0)
        {
            struct geometry geometry;

            /* Not FLT MTD device, get normal block geometry */

            ret = driver->u.i_bops->geometry (driver, &geometry);
            if (ret >= 0)
            {
                /* And convert to MTD geometry */

                fs->geo.blocksize    = geometry.geo_sectorsize;
                fs->geo.erasesize    = geometry.geo_sectorsize;
                fs->geo.neraseblocks = geometry.geo_nsectors;
            }
        }
    }

    if (ret < 0)
    {
        goto errout_with_fs;
    }

    /* Initialize lwext4 block driver structure */

    fs->blkdev.part_offset = 0;
    fs->blkdev.part_size   = (uint64_t)fs->geo.erasesize * (uint64_t)fs->geo.neraseblocks;
    snprintf (fs->mount_path, sizeof (fs->mount_path), "/ext4_%d/",
              g_lwext4_index++);

    iface->ph_bbuf = malloc (fs->geo.blocksize);
    if (!iface->ph_bbuf)
    {
        ret = -ENOMEM;
        goto errout_with_iface_buf;
    }

    iface->open     = lwext4_blkdev_open;
    iface->bread    = lwext4_read_block;
    iface->bwrite   = lwext4_write_block;
    iface->close    = lwext4_blkdev_close;
    iface->lock     = lwext4_blklock;
    iface->unlock   = lwext4_blkunlock;
    iface->ph_bsize = fs->geo.blocksize;
    iface->ph_bcnt  = fs->geo.neraseblocks * (fs->geo.erasesize / fs->geo.blocksize);
    iface->p_user   = fs;
    TTOS_CreateMutex (1, 0, &fs->blk_lock);
    ext4_device_register (&fs->blkdev, driver->i_name);

    /* Then get information about the lwext4 filesystem on the devices
     * managed by this driver.
     */

    /* Force format the device if -o forceformat */

    if (data && strcmp (data, "forceformat") == 0)
    {
        struct ext4_mkfs_info info;
        struct ext4_fs        ext4_tmpfs;
        memset (&info, 0, sizeof info);
        info.block_size = 4096;
        info.journal    = true;
        memset(&ext4_tmpfs, 0, sizeof(ext4_tmpfs));
        ret             = lwext4_convert_result (
            ext4_mkfs (&ext4_tmpfs, &fs->blkdev, &info, F_SET_EXT4));
        if (ret < 0)
        {
            goto errout_with_fs;
        }
    }

    ret = lwext4_convert_result (
        ext4_mount (driver->i_name, fs->mount_path, false));
    if (ret < 0)
    {
        struct ext4_mkfs_info info;
        struct ext4_fs        ext4_tmpfs;
        /* Auto format the device if -o autoformat */

        if (/*ret != -EFAULT ||  */ !data || strcmp (data, "autoformat"))
        {
            goto errout_with_fs;
        }
        memset (&info, 0, sizeof info);
        info.block_size = 4096;
        info.journal    = true;
        memset(&ext4_tmpfs, 0, sizeof(ext4_tmpfs));
        ret = lwext4_convert_result (
            ext4_mkfs (&ext4_tmpfs, &fs->blkdev, &info, F_SET_EXT4));
        if (ret < 0)
        {
            printk ("mkfs error\n");
            goto errout_with_fs;
        }

        /* Try to mount the device again */

        ret = lwext4_convert_result (
            ext4_mount (driver->i_name, fs->mount_path, false));
        if (ret < 0)
        {
            printk ("mount error\n");
            goto errout_with_fs;
        }
    }

    TTOS_CreateMutex (1, 0, &lock);

    ext4_lock.lock   = ext4_mutexlock;
    ext4_lock.unlock = ext4_mutexunlock;

    ext4_mount_setup_locks (fs->mount_path, &ext4_lock);

    ret = ext4_recover (fs->mount_path);
    if (ret != EOK && ret != ENOTSUP)
    {
        printk ("ext4_recover: rc = %d\n", ret);
        goto errout_with_iface_buf;
    }

    ret = ext4_journal_start (fs->mount_path);
    if (ret != EOK)
    {
        printk ("ext4_journal_start: rc = %d\n", ret);
        goto errout_with_iface_buf;
    }

    ext4_cache_write_back (fs->mount_path, 0);
    *handle = fs;
    return EOK;

errout_with_iface_buf:
    free (iface->ph_bbuf);
errout_with_iface:
    free (iface);
errout_with_fs:
    free (fs);
errout_with_block:
    if (INODE_IS_BLOCK (driver) && driver->u.i_bops->close)
    {
        driver->u.i_bops->close (driver);
    }

    return ret;
}

/****************************************************************************
 * Name: lwext4_unbind
 *
 * Description: This implements the filesystem portion of the umount
 *  operation.
 *
 ****************************************************************************/

static int lwext4_unbind (void *handle, struct inode **driver,
                          unsigned int flags)
{
    struct lwext4_mountpt_s *fs  = handle;
    struct inode            *drv = fs->drv;
    int                      ret = 0;

    /* Unmount */

    ext4_cache_write_back (fs->mount_path, 0);

    ret = ext4_journal_stop (fs->mount_path);
    if (ret != EOK)
    {
        printk ("ext4_journal_stop: fail %d", ret);
        return false;
    }

    ret = lwext4_convert_result (ext4_umount (fs->mount_path));

    if (ret >= 0)
    {
        /* Close the block driver */

        if (INODE_IS_BLOCK (drv) && drv->u.i_bops->close)
        {
            drv->u.i_bops->close (drv);
        }

        /* We hold a reference to the driver but should not but
         * mucking with inodes in this context. So, we will just return
         * our contained reference to the driver inode and let the
         * umount logic dispose of it.
         */

        if (driver)
        {
            *driver = drv;
        }

        /* Release the mountpoint private data */

        ext4_device_unregister ((const char *)fs->mount_path);
        free (fs->blkdev.bdif->ph_bbuf);
        free (fs->blkdev.bdif);
        free (fs);
    }

    return ret;
}

/****************************************************************************
 * Name: lwext4_statfs
 *
 * Description: Return filesystem statistics
 *
 ****************************************************************************/

static int lwext4_statfs (struct inode *mountpt, struct statfs *buf)
{
    struct lwext4_mountpt_s *fs;
    struct ext4_mount_stats  stats;
    int                      ret = 0;

    /* Get the mountpoint private data from the inode structure */

    fs = mountpt->i_private;

    /* Return something for the file system description */

    buf->f_type    = EXT4_SUPERBLOCK_MAGIC;
    buf->f_namelen = PATH_MAX;

    ext4_mutexlock();

    ret = lwext4_convert_result (
        ext4_mount_point_stats (fs->mount_path, &stats));

    ext4_mutexunlock();
    if (ret < 0)
    {
        return ret;
    }

    buf->f_bsize  = stats.block_size;
    buf->f_blocks = stats.blocks_count;
    buf->f_bfree  = stats.free_blocks_count;
    buf->f_bavail = stats.free_blocks_count;

    return ret;
}

/****************************************************************************
 * Name: lwext4_unlink
 *
 * Description: Remove a file
 *
 ****************************************************************************/

static int lwext4_unlink (struct inode *mountpt, const char *relpath)
{
    struct lwext4_mountpt_s *fs;
    int                      ret = 0;
    char                     fullpath[PATH_MAX];

    /* Get the mountpoint private data from the inode structure */

    fs = mountpt->i_private;

    strcpy (fullpath, fs->mount_path);
    strcat (fullpath, relpath);

    /* Call the LFS to perform the unlink */
    ext4_mutexlock();
    ret = lwext4_convert_result (ext4_fremove (fullpath));
    ext4_mutexunlock();

    return ret;
}

/****************************************************************************
 * Name: lwext4_mkdir
 *
 * Description: Create a directory
 *
 ****************************************************************************/

static int lwext4_mkdir (struct inode *mountpt, const char *relpath,
                         mode_t mode)
{
    struct lwext4_mountpt_s *fs;
    int                      ret = 0;
    char                     fullpath[PATH_MAX];
    if (!relpath || (relpath && (relpath[0] == 0)))
    {
        return -ENOENT;
    }
    /* Get the mountpoint private data from the inode structure */
    fs = mountpt->i_private;

    strcpy (fullpath, fs->mount_path);
    strcat (fullpath, relpath);
    /* Call LFS to do the mkdir */
    ext4_mutexlock();
    ret = ext4_dir_mk (fullpath);
    ext4_mutexunlock();

    return ret;
}

/****************************************************************************
 * Name: lwext4_rmdir
 *
 * Description: Remove a directory
 *
 ****************************************************************************/

static int lwext4_rmdir (struct inode *mountpt, const char *relpath)
{
    struct stat              buf;
    struct lwext4_mountpt_s *fs;
    int                      ret = 0;
    char                     fullpath[PATH_MAX];
    fs = mountpt->i_private;

    strcpy (fullpath, fs->mount_path);
    strcat (fullpath, relpath);

    ext4_mutexlock();
    ret = lwext4_stat (mountpt, relpath, &buf);
    if (ret < 0)
    {
        ext4_mutexunlock();
        return ret;
    }

    if (S_ISDIR (buf.st_mode))
    {
        ext4_mutexunlock();
        return ext4_dir_rm (fullpath);
    }
    else
    {
        ext4_mutexunlock();
        return -ENOTDIR;
    }
}

/****************************************************************************
 * Name: lwext4_rename
 *
 * Description: Rename a file or directory
 *
 ****************************************************************************/

static int lwext4_rename (struct inode *mountpt, const char *oldrelpath,
                          const char *newrelpath)
{
    struct lwext4_mountpt_s *fs;
    int                      ret = 0;
    char                     oldfullpath[PATH_MAX];
    char                     newfullpath[PATH_MAX];
    /* Get the mountpoint private data from the inode structure */

    fs = mountpt->i_private;

    strcpy (oldfullpath, fs->mount_path);
    strcpy (newfullpath, fs->mount_path);
    strcat (oldfullpath, oldrelpath);
    strcat (newfullpath, newrelpath);

    /* Call LFS to do the rename */
    ext4_mutexlock();
    ret = lwext4_convert_result (ext4_frename (oldfullpath, newfullpath));
    ext4_mutexunlock();

    return ret;
}

/****************************************************************************
 * Name: lwext4_stat
 *
 * Description: Return information about a file or directory
 *
 ****************************************************************************/

static int lwext4_stat (struct inode *mountpt, const char *relpath,
                        struct stat *buf)
{
    struct lwext4_mountpt_s *fs;
    int                      ret = 0;
    uint32_t                 ret_ino;
    struct ext4_inode        ext4_inode;
    struct ext4_sblock      *sb;
    struct mtd_geometry_s   *geo;

    char *fullpath = NULL;

    memset (buf, 0, sizeof (*buf));

    /* Get the mountpoint private data from the inode structure */

    fs  = mountpt->i_private;
    geo = &fs->geo;

    asprintf (&fullpath, "%s%s", fs->mount_path, relpath);
    if (fullpath == NULL)
    {
        return -ENOMEM;
    }
    ext4_mutexlock();
    /* Call the LFS to do the stat operation */

    ret = lwext4_convert_result (ext4_get_sblock (fs->mount_path, &sb));
    if (ret < 0)
    {
        goto out;
    }

    ret = lwext4_convert_result (
        ext4_raw_inode_fill (fullpath, &ret_ino, &ext4_inode));
    if (ret < 0)
    {
        goto out;
    }

    /* Convert info to stat */
    ret = lwext4_convert_result (ext4_mode_get (fullpath, &buf->st_mode));
    if (ret < 0)
    {
        goto out;
    }

    if (ext4_inode_is_type (sb, &ext4_inode, EXT4_INODE_MODE_DIRECTORY))
    {
        buf->st_size = 0;
    }
    else
    {
        buf->st_size
            = (uint64_t)ext4_inode.size_lo | (uint64_t)ext4_inode.size_hi << 32;
    }
    buf->st_blksize = geo->blocksize;
    buf->st_blocks  = (buf->st_size + buf->st_blksize - 1) / buf->st_blksize;
    buf->st_atim.tv_sec = ext4_inode_get_access_time (&ext4_inode);
    buf->st_mtim.tv_sec = ext4_inode_get_modif_time (&ext4_inode);
    buf->st_ctim.tv_sec = ext4_inode_get_change_inode_time (&ext4_inode);
    buf->st_uid         = ext4_inode_get_uid (&ext4_inode);
    buf->st_gid         = ext4_inode_get_gid (&ext4_inode);
    /* 在执行cp命令时，覆盖拷贝，提示相同文件（在busybox代码中使用st_dev和i_ino作为相同文件判断条件）
     */
    buf->st_dev = ext4_inode_get_dev (&ext4_inode);
    buf->st_ino = ret_ino;

out:
    ext4_mutexunlock();
    free (fullpath);
    return ret;
}

static int lwext4_fchstat (const struct file *filep, const struct stat *buf,
                           int flags)
{
    struct lwext4_mountpt_s *fs;
    struct lwext4_file_s    *priv;
    struct inode            *inode;
    struct ext4_inode_ref    inode_ref;
    struct ext4_sblock      *sb;
    int                      ret = 0;

    /* Recover our private data from the struct file instance */

    priv  = filep->f_priv;
    inode = filep->f_inode;
    fs    = inode->i_private;

    ext4_mutexlock();
    /* Call LFS to get file size */

    ret = ext4_get_sblock (fs->mount_path, &sb);
    if (ret < 0)
    {
        goto out;
    }

    /*Load parent*/
    ret = ext4_fs_get_inode_ref (fs->blkdev.fs, priv->file.inode, &inode_ref);
    if (ret < 0)
    {
        goto out;
    }

    if (CH_STAT_MODE & flags)
    {
        ext4_inode_set_mode (sb, inode_ref.inode, buf->st_mode);
    }

    if (CH_STAT_UID & flags)
    {
        ext4_inode_set_uid (inode_ref.inode, buf->st_uid);
    }

    if (CH_STAT_GID & flags)
    {
        ext4_inode_set_gid (inode_ref.inode, buf->st_gid);
    }

    if (CH_STAT_ATIME & flags)
    {
        ext4_inode_set_access_time (inode_ref.inode, buf->st_atim.tv_sec);
    }

    if (CH_STAT_MTIME & flags)
    {
        ext4_inode_set_modif_time (inode_ref.inode, buf->st_mtim.tv_sec);
    }

    ret = ext4_fs_put_inode_ref (&inode_ref);

out:
    ext4_mutexunlock();
    return ret;
}

static int lwext4_chstat (struct inode *mountpt, const char *relpath,
                          const struct stat *buf, int flags)
{
    struct lwext4_mountpt_s *fs;
    int                      ret = 0;

    char *fullpath = NULL;

    /* Get the mountpoint private data from the inode structure */

    fs = mountpt->i_private;

    asprintf (&fullpath, "%s%s", fs->mount_path, relpath);
    if (fullpath == NULL)
    {
        return -ENOMEM;
    }

    ext4_mutexlock();
    if (CH_STAT_MODE & flags)
    {
        ret = lwext4_convert_result (ext4_mode_set (fullpath, buf->st_mode));
        if (ret < 0)
        {
            goto out;
        }
    }

    if ((CH_STAT_UID & flags) && (CH_STAT_GID & flags))
    {
        ret = lwext4_convert_result (
            ext4_owner_set (fullpath, buf->st_uid, buf->st_gid));
        if (ret < 0)
        {
            goto out;
        }
    }

    if (CH_STAT_ATIME & flags)
    {
        ret = lwext4_convert_result (
            ext4_atime_set (fullpath, buf->st_atim.tv_sec));
        if (ret < 0)
        {
            goto out;
        }
    }

    if (CH_STAT_MTIME & flags)
    {
        ret = lwext4_convert_result (
            ext4_mtime_set (fullpath, buf->st_mtim.tv_sec));
        if (ret < 0)
        {
            goto out;
        }
    }

out:
    ext4_mutexunlock();
    free (fullpath);
    return ret;
}

static ssize_t lwext4_readlink (struct inode *mountpt, const char *relpath,
                                char *buf, size_t bufsize)
{
    int                      ret = 0;
    size_t                   rcnt;
    struct lwext4_mountpt_s *fs;
    char                    *fullpath = NULL;

    /* Get the mountpoint private data from the inode structure */

    fs = mountpt->i_private;

    asprintf (&fullpath, "%s%s", fs->mount_path, relpath);
    if (fullpath == NULL)
    {
        return -ENOMEM;
    }
    ext4_mutexlock();
    ret = lwext4_convert_result (ext4_readlink (fullpath, buf, bufsize, &rcnt));
    ext4_mutexunlock();
    free (fullpath);

    if ((ret < 0) || (rcnt == 0))
    {
        return -EINVAL;
    }
    return rcnt;
}

static int lwext4_symlink (struct inode *mountpt, const char *target,
                           const char *link_relpath)
{
    int                      ret = 0;
    char                    *fullpath;
    struct lwext4_mountpt_s *fs;
    fs = mountpt->i_private;

    asprintf (&fullpath, "%s%s", fs->mount_path, link_relpath);
    if (fullpath == NULL)
    {
        return -ENOMEM;
    }
    ext4_mutexlock();
    ret = lwext4_convert_result (ext4_fsymlink (target, fullpath));
    ext4_mutexunlock();
    free (fullpath);
    return ret;
}

static char * lwext4_get_internal_path (const char *target)
{
    struct inode_search_s desc;
    struct inode         *inode;
    int                   ret = 0;
    char * internal_path;

    /* Sanity checks */

    if (target == NULL || target == NULL)
    {
        ret = -EFAULT;
        goto errout;
    }

    if (*target == '\0')
    {
        ret = -ENOENT;
        goto errout;
    }

    char *linkpath = malloc (PATH_MAX);
    if (linkpath == NULL)
    {
        errno = ENOMEM;
        return NULL;
    }
    if (realpath (target, linkpath) == NULL)
    {
        free (linkpath);
        return NULL;
    }

    /* Get an inode for this file */

    SETUP_SEARCH (&desc, linkpath, false);

    ret = inode_find (&desc);

    free (linkpath);

    if (ret < 0)
    {
        /* This name does not refer to a psudeo-inode and there is no
         * mountpoint that includes in this path.
         */

        goto errout_with_search;
    }

    /* Get the search results */

    inode = desc.node;
    assert (inode != NULL);

    /* The way we handle the statfs depends on the type of inode that we
     * are dealing with.
     */

#ifndef CONFIG_DISABLE_MOUNTPOINT

    if (INODE_IS_MOUNTPT (inode))
    {
        /* The node is a file system mointpoint. Verify that the mountpoint
         * supports the statfs() method
         */
        struct lwext4_mountpt_s *fs_target;
        fs_target = inode->i_private;

        asprintf (&internal_path, "%s%s", fs_target->mount_path, desc.relpath);
        if (internal_path == NULL)
        {
            errno = ENOMEM;
            return NULL;
        }
    }
    else
#endif
    {
        errno = ENOENT;
        return NULL;
    }

    /* Check if the statfs operation was successful */

    if (ret < 0)
    {
        goto errout_with_inode;
    }

    /* Successfully statfs'ed the file */

    inode_release (inode);
    RELEASE_SEARCH (&desc);
    return internal_path;

    /* Failure conditions always set the errno appropriately */

errout_with_inode:
    inode_release (inode);

errout_with_search:
    RELEASE_SEARCH (&desc);

errout:
    errno = (-ret);
    return NULL;
}

static int lwext4_link (struct inode *mountpt, const char *target,
                        const char *link_relpath)
{
    int                      ret = 0;
    char                    *fullpath;
    char                    *fullpath_target;
    struct lwext4_mountpt_s *fs;
    
    fs = mountpt->i_private;
    struct statfs           buf;
    struct file file;
    
    ret = vfs_statfs (target, &buf);
    if(ret < 0)
    {
        return ret;
    }

    if(buf.f_type != EXT4_SUPERBLOCK_MAGIC)
    {
        return -ENOTSUP;
    }

    asprintf (&fullpath, "%s%s", fs->mount_path, link_relpath);
    if (fullpath == NULL)
    {
        return -ENOMEM;
    }
    fullpath_target = lwext4_get_internal_path(target);
    if(fullpath_target == NULL)
    {
        free (fullpath);
        return -ENOENT;
    }
    
    ext4_mutexlock();
    ret = lwext4_convert_result (ext4_flink (fullpath_target, fullpath));
    ext4_mutexunlock();
    free (fullpath);
    free (fullpath_target);
    return ret;
}
