/**
 * @file    kernel/process/signal.c
 * <AUTHOR>
 * @brief
 * @version 3.0.0
 * @date    2024-07-30
 *
 * 7784b93c 2024-07-25 解决启动卡住的问题
 * 0bf375fe 2024-07-19 修复time部分行为逻辑
 * 77a67901 2024-07-18 添加内核对用户态time相关的支持
 * b215ad13 2024-07-18 添加信号唤醒futex
 * 309c9263 2024-07-18 修改错误码不正确的问题
 * b182ca5c 2024-07-17 coredump功能支持：在用户态发生异常时保存异常任务现场，并通过gdb查看
 * afa87c18 2024-07-17 修改错误码
 * 914d9323 2024-07-11 添加周期任务支持
 * 2572306a 2024-07-09 修复可能造成释放内存后还在使用的bug
 * 3fc2b591
 * 2024-07-05 1.处理信号handler第二三个参数不正确的问题 2.标准化部分数据结构 3.添加部分调试信息 4.添加rt_sigqueueinfo系统调用
 * 5.waitqueue.c中将spin_lock_irq改spin_lock_irqsave 77357fcd
 * 2024-07-03 1.添加mknod系统调用 2.修复获取信号mask错误的bug 3.修复rt_sigpending返回值未设置的问题 4.修改mmap_kernel_addr_to_user实现
 * 5.shmfs添加释放互斥锁操作 ac006b61 2024-07-02 移除一级ttos目录 6e0b57ed 2024-07-01
 * 修改sigwaitinfo获取到信息不正确的问题 92e22a0f 2024-07-01 添加内核tid 以解决tid反查task的问题
 * 33a1f51a 2024-06-27 alarm 功能实现
 * 4bd87aa6 2024-06-27 添加 setitimer syscall
 * 5da3da96 2024-06-26 修改进程业务逻辑 只有leader 才有父子进程树
 * 43526576 2024-06-26 1.调整函数命名并添加部分注释 2.解决部分汇编指令传入参数有误的问题
 * 2d8e98f1 2024-06-25 调整打印级别
 * 31d8b130 2024-06-22 解决kernel_sigtimedwait返回值可能不正确的问题
 * e70e2035 2024-06-21 修复由于关调度导致任务未正常退出的问题。
 * 9f8f9686 2024-06-20 修改编译错误
 * 04f62be0 2024-06-20 将使用spin_lock_irq改为使用spin_lock_irqsave
 * 132d1863 2024-06-18 解决编译问题
 * 8dcdaef2 2024-06-17 do_execve中添加信号处理。
 * a228b9be 2024-06-17 修复获取signal old action错误的问题。
 * 5c89b44a 2024-06-12 修改stop_process实现
 * 03f6ba78 2024-06-12 修复线程组任务退出机制，由强制删除改为发送信号
 * 6ca0a6ef
 * 2024-06-11 1.处理TTOS_ObtainSema失败时，未将信号量值加回去的问题。 2.处理信号量未被信号中断问题
 * 4dc228c0 2024-06-07 处理进程退出
 * b9d7bcd8 2024-06-06 修改调试打印等级
 * c51b2b63 2024-06-06 修复在进行信号处理时，设置信号屏蔽码有误的问题。
 * 031eb404 2024-06-06 修改sigqueue_dequeue函数实现
 * 1f357380 2024-06-06 signal bug fix
 * ef4a0f3b 2024-06-05 更新日志打印
 * ceff8d87 2024-06-05 修改klog打印到内核环缓冲，不再直接打印到串口
 * 3ad5e448 2024-06-04 临时提交
 * 75a6a3c3 2024-05-24 代码整理
 * 702dfdb6 2024-05-31 修复用户态互斥锁问题
 * 4bb423b7 2024-05-30 1.添加进程组 2.支持信号发送到进程组
 * ae75b13f 2024-05-24 代码整理
 * 865ecac5 2024-05-23 正确处理进程异常
 * 3823374e 2024-05-23 添加signal功能模块
 *
 * 科东(广州)软件科技有限公司 版权所有
 * @copyright Copyright (C) 2023 Intewell Inc. All Rights Reserved.
 */

#include <assert.h>
#include <errno.h>
#include <inttypes.h>
#include <ipi.h>
#include <process_signal.h>
#include <ptrace/ptrace.h>
#include <signal.h>
#include <ttos.h>
#include <ttosBase.h>
#include <ttosProcess.h>
#include <ttos_init.h>
#include <ttos_pic.h>
#include <uaccess.h>

#include <coredump.h>
#include <period_sched_group.h>
#include <tasklist_lock.h>
#include <tglock.h>
#include <time/ktime.h>
#include <wqueue.h>

#undef KLOG_TAG
#define KLOG_TAG "signal"
#include <klog.h>

#undef KLOG_D
#define KLOG_D(...)

#define PRIVATE_SIGQ(tp) (&(tp)->sig_queue)

struct signal_work_param
{
    pid_t pid;
    int to;
    int signal;
    int si_code;
    ksiginfo_t *kinfo;
    struct work_s work;
};

unsigned long ttos_ffz(unsigned long x);
int waitqueue_forced_remove(T_TTOS_TaskControlBlock *task);
bool is_stop_set(int signo);

/* 信号等待的任务队列 */
extern T_TTOS_Task_Queue_Control signalsWaitQueue;
extern T_TTOS_Task_Queue_Control signalsWaitPendingQueue;

static process_sigset_t *_mask_set_fn(pcb_t thread, const process_sigset_t *sigset,
                                      process_sigset_t *new_set)
{
    memcpy(new_set, sigset, sizeof(*sigset));
    return new_set;
}

void _sigorsets(process_sigset_t *dset, const process_sigset_t *set0, const process_sigset_t *set1)
{
    switch (_PROCESS_NSIG_WORDS)
    {
    case 4:
        dset->sig[3] = set0->sig[3] | set1->sig[3];
        dset->sig[2] = set0->sig[2] | set1->sig[2];
    case 2:
        dset->sig[1] = set0->sig[1] | set1->sig[1];
    case 1:
        dset->sig[0] = set0->sig[0] | set1->sig[0];
    default:
        return;
    }
}

static process_sigset_t *_mask_block_fn(pcb_t pcb, const process_sigset_t *sigset,
                                        process_sigset_t *new_set)
{
    _sigorsets(new_set, &pcb->blocked, sigset);
    return new_set;
}

void _signotsets(process_sigset_t *dset, const process_sigset_t *set)
{
    switch (_PROCESS_NSIG_WORDS)
    {
    case 4:
        dset->sig[3] = ~set->sig[3];
        dset->sig[2] = ~set->sig[2];
    case 2:
        dset->sig[1] = ~set->sig[1];
    case 1:
        dset->sig[0] = ~set->sig[0];
    default:
        return;
    }
}

int _sigisemptyset(process_sigset_t *set)
{
    if (set == NULL)
    {
        return 1;
    }
    switch (_PROCESS_NSIG_WORDS)
    {
    case 4:
        return (set->sig[3] | set->sig[2] | set->sig[1] | set->sig[0]) == 0;
    case 2:
        return (set->sig[1] | set->sig[0]) == 0;
    case 1:
        return set->sig[0] == 0;
    default:
        return 1;
    }
}

int sigqueue_isempty(process_sigqueue_t sigqueue)
{
    return _sigisemptyset(&sigqueue->sigset_pending);
}

void _sigandsets(process_sigset_t *dset, const process_sigset_t *set0, const process_sigset_t *set1)
{
    switch (_PROCESS_NSIG_WORDS)
    {
    case 4:
        dset->sig[3] = set0->sig[3] & set1->sig[3];
        dset->sig[2] = set0->sig[2] & set1->sig[2];
    case 2:
        dset->sig[1] = set0->sig[1] & set1->sig[1];
    case 1:
        dset->sig[0] = set0->sig[0] & set1->sig[0];
    default:
        return;
    }
}

static process_sigset_t *_mask_unblock_fn(pcb_t thread, const process_sigset_t *sigset,
                                          process_sigset_t *new_set)
{
    process_sigset_t complement;
    _signotsets(&complement, sigset);
    _sigandsets(new_set, &thread->blocked, &complement);
    return new_set;
}

static process_sigset_t *(*_sig_mask_fn[__PROCESS_SIG_MASK_CMD_WATERMARK])(
    pcb_t pcb, const process_sigset_t *sigset, process_sigset_t *new_set) = {
    [PROCESS_SIG_MASK_CMD_BLOCK] = _mask_block_fn,
    [PROCESS_SIG_MASK_CMD_UNBLOCK] = _mask_unblock_fn,
    [PROCESS_SIG_MASK_CMD_SET_MASK] = _mask_set_fn,
};

void pcb_lock(pcb_t pcb, long *flags)
{
    unsigned long irq_flag;
    // spin_lock_irqsave(&pcb->lock, irq_flag);
    
    //TTOS_KERNEL_LOCK();
    //TBSP_GLOBALINT_DISABLE_WITH_LOCK(irq_flag);
    ttos_int_lock(irq_flag);
    *flags = irq_flag;
}

void pcb_unlock(pcb_t pcb, long *flags)
{
    unsigned long irq_flag = *flags;
    // spin_unlock_irqrestore(&pcb->lock, irq_flag);
    //TTOS_KERNEL_UNLOCK();
    //TBSP_GLOBALINT_ENABLE_WITH_LOCK(irq_flag);
    ttos_int_unlock(irq_flag);
}

void sighand_lock (pcb_t pcb, long *irq_flag)
{
    irq_flags_t flag;
    struct sighand_struct *sighand = NULL;

    assert(!!pcb);

    sighand = get_process_sighand(pcb);

    spin_lock_irqsave(&sighand->siglock, flag);

    *irq_flag = flag;
}

void sighand_unlock(pcb_t pcb, long *irq_flag)
{
    irq_flags_t flag = *irq_flag;
    struct sighand_struct *sighand = NULL;

    assert(!!pcb);

    sighand = get_process_sighand(pcb);

    spin_unlock_irqrestore(&sighand->siglock, flag);
}

static process_siginfo_t *siginfo_create(int signo, int code, ksiginfo_t *info)
{
    process_siginfo_t *siginfo = NULL;
    pcb_t cur_pcb;

    siginfo = calloc(1, sizeof(process_siginfo_t));
    if (siginfo)
    {
        if (info)
        {
            memcpy(&siginfo->ksiginfo, info, sizeof(siginfo->ksiginfo));
        }

        cur_pcb = ttosProcessSelf();
        if (cur_pcb && (code == SI_USER))
        {
            siginfo->ksiginfo.si_pid = get_process_pid(cur_pcb);
            siginfo->ksiginfo.si_uid = 0;
        }

        siginfo->ksiginfo.si_signo = signo;
        siginfo->ksiginfo.si_code = code;
    }

    return siginfo;
}

void siginfo_delete(process_siginfo_t *siginfo)
{
    if (siginfo)
    {
        free(siginfo);
    }
}

int _sigismember(process_sigset_t *set, int _sig)
{
    unsigned long sig = _sig - 1;

    if (_PROCESS_NSIG_WORDS == 1)
    {
        return 1 & (set->sig[0] >> sig);
    }
    else
    {
        return 1 & (set->sig[sig / _PROCESS_NSIG_BPW] >> (sig % _PROCESS_NSIG_BPW));
    }
}

int sigqueue_ismember(process_sigqueue_t sigqueue, int signo)
{
    return _sigismember(&sigqueue->sigset_pending, signo);
}

static pcb_t _signal_find_catcher(pcb_t pcb, int signo)
{
    pcb_t catcher = NULL;
    pcb_t candidate;

    // if (!_sigismember(&pcb->blocked, signo))
    // {
    //     catcher = pcb;
    //     return catcher;
    // }

    tg_lock(pcb->group_leader);
    /* 查找线程组中 未屏蔽该信号的线程||屏蔽了该信号&&处于TTOS_TASK_WAITING_SIGNAL_PENDING的线程 */
    list_for_each_entry(candidate, &pcb->group_leader->thread_group, sibling_node)
    {
        if (!_sigismember(&candidate->blocked, signo) ||
            (pcb->taskControlId->state & TTOS_TASK_WAITING_SIGNAL_PENDING))
        {
            if (!catcher)
            {
                catcher = candidate;
            }
            else
            {
                /* 优先选择优先级更高的任务作为候选任务 */
                if (candidate->taskControlId->taskCurPriority < catcher->taskControlId->taskCurPriority)
                {
                    catcher = candidate;
                }
            }
        }
    }
    tg_unlock(pcb->group_leader);

    if (!catcher)
    {
        catcher = pcb->group_leader;
    }

    return catcher;
}

int sigequalsets(const process_sigset_t *set1, const process_sigset_t *set2)
{
    switch (_PROCESS_NSIG_WORDS)
    {
    case 4:
        return (set1->sig[3] == set2->sig[3]) && (set1->sig[2] == set2->sig[2]) &&
               (set1->sig[1] == set2->sig[1]) && (set1->sig[0] == set2->sig[0]);
    case 2:
        return (set1->sig[1] == set2->sig[1]) && (set1->sig[0] == set2->sig[0]);
    case 1:
        return set1->sig[0] == set2->sig[0];
    }
    return 0;
}

void _sigaddset(process_sigset_t *set, int _sig)
{
    unsigned long sig = _sig - 1;

    if (_PROCESS_NSIG_WORDS == 1)
    {
        set->sig[0] |= 1UL << sig;
    }
    else
    {
        set->sig[sig / _PROCESS_NSIG_BPW] |= 1UL << (sig % _PROCESS_NSIG_BPW);
    }
}

void signal_set_running(int signo)
{
    if (signo <= 0 || signo >= SIGNAL_MAX)
    {
        return;
    }

    pcb_t pcb = ttosProcessSelf()->group_leader;
    process_sigset_t *running = &pcb->sig_queue.sigset_running;

    _sigaddset(running, signo);
}

static void sigqueue_enqueue(process_sigqueue_t sigqueue, process_siginfo_t *siginfo)
{
    bool inserted = false;

    /* 链表不为空，插入最后 */
    if (!list_is_empty(&sigqueue->siginfo_list))
    {
        list_add_after(&siginfo->node, &sigqueue->siginfo_list);
        inserted = true;
    }

    /* 链表为空 || 链表中所有元素的信号值都比siginfo中的信号值小，则插入到头 */
    if (!inserted)
        list_add_before(&siginfo->node, &sigqueue->siginfo_list);

    /* 加入pending信号集 */
    _sigaddset(&sigqueue->sigset_pending, siginfo->ksiginfo.si_signo);
    return;
}

int wakeup_thread(T_TTOS_TaskControlBlock *tcb)
{
    assert(tcb != NULL);

    if (TTOS_OK != TTOS_ResumeTask(tcb))
    {
        KLOG_E("%s fail", __func__);
        return -1;
    }
    return 0;
}

bool task_is_interruptible(T_TTOS_TaskControlBlock *tcb, unsigned int sig)
{
    unsigned int state = tcb->state;

    if ((sig == SIGKILL || sig == SIGSTOP) ||
        (state & TTOS_TASK_INTERRUPTIBLE_BY_SIGNAL) == TTOS_TASK_INTERRUPTIBLE_BY_SIGNAL)
    {
        return true;
    }
    else
    {
        return false;
    }
}

void signal_wakeup_task(T_TTOS_TaskControlBlock *tcb, process_siginfo_t *siginfo)
{
    int task_stat = tcb->state;

    /* 设置任务被信号中断标识 */
    tcb->wait.returnCode = TTOS_SIGNAL_INTR;

    memset(&tcb->wait.sig_info, 0, sizeof(tcb->wait.sig_info));

    /* 记录信号信息 */
    memcpy(&tcb->wait.sig_info, siginfo, sizeof(tcb->wait.sig_info));

    /* 任务在计时等待 */
    if (tcb->objCore.objectNode.next != NULL)
    {
        /* 将task任务从任务tick等待队列中移除 */
        ttosExactWaitedTask(tcb);
    }

    if (tcb->state & TTOS_TASK_WAITING_TIME_EVENT)
    {
        waitqueue_forced_remove(tcb);
    }

    /* 将当前任务从其等待队列中移出 */
    ttosExtractTaskq(tcb->wait.queue, tcb);

    /* 被stop信号停止 */
    if (task_stat & TTOS_TASK_STOPPED_BY_SIGNAL)
    {
        TTOS_SignalResumeTask(tcb);
    }

    if (TTOS_TASK_SUSPEND == task_stat)
    {
        TTOS_ResumeTask(tcb);
    }

    /* 清除task任务的等待状态 */
    ttosClearTaskWaiting(tcb);
}

static void do_signal_notify(pcb_t pcb, process_siginfo_t *siginfo)
{
    int sig = 0;
    irq_flags_t msr;
    unsigned int stat;
    T_TTOS_TaskControlBlock *tcb;

    if (!pcb)
    {
        return;
    }

    sig = siginfo->ksiginfo.si_signo;

    ttosDisableTaskDispatchWithLock();
    ttos_int_lock(msr);

    tcb = pcb->taskControlId;
    stat = tcb->state;

    /* 考虑非自启动的周期任务处理 */
    if (TTOS_SCHED_PERIOD == tcb->taskType)
    {
        if (stat == TTOS_TASK_DORMANT)
        {
            tcb->periodNode.delayTime = 0;
            period_thread_active_single(pcb);
            ttos_int_unlock(msr);
            ttosEnableTaskDispatchWithLock();
            return;
        }
    }

    if (TTOS_TASK_READY & stat)
    {
        ttos_int_unlock(msr);
        ttosEnableTaskDispatchWithLock();

        return;
    }

    /* 如果信号未被线程屏蔽||(已经屏蔽了,则sig处于pending状态,检查任务是否处于TTOS_TASK_WAITING_SIGNAL_PENDING)
     */
    if (!_sigismember(&pcb->blocked, sig) || (stat & TTOS_TASK_WAITING_SIGNAL_PENDING))
    {
        if (task_is_interruptible(tcb, sig))
        {
            signal_wakeup_task(tcb, siginfo);
            KLOG_D("pcb:%p wakedup by signal %d", pcb, sig);
        }
    }

    ttos_int_unlock(msr);
    ttosEnableTaskDispatchWithLock();
}

process_sighandler_t get_sighandler(pcb_t pcb, int signo)
{
    struct sighand_struct *sighand = get_process_sighand(pcb);

    return sighand->sig_action[signo - 1].__sa_handler.sa_handler;
}

bool in_ignored_set(int signo)
{
    process_sigset_t ign_set = process_sigset_init(PROCESS_SIG_IGNORE_SET);
    return _sigismember(&ign_set, signo);
}

bool sig_ignored(pcb_t pcb, int signo)
{
    bool is_ignored;
    process_sighandler_t action;
    process_sigset_t ign_set = process_sigset_init(PROCESS_SIG_IGNORE_SET);

    action = get_sighandler(pcb, signo);

    /* 被阻塞的信号不能被忽略 */
    if (_sigismember(&pcb->blocked, signo) || _sigismember(&pcb->real_blocked, signo))
    {
        return false;
    }

    if ((pcb->ptrace & PT_PTRACED) && signo != SIGKILL)
    {
        return false;
    }

    if (action == PROCESS_SIG_ACT_IGN)
    {
        return true;
    }
    else if (action == PROCESS_SIG_ACT_DFL && _sigismember(&ign_set, signo))
    {
        return true;
    }
    else
    {
        return false;
    }
}

bool is_rt_signo(int signo)
{
    return (signo >= SIGRTMIN && signo <= SIGRTMAX) ? true : false;
}

static bool signal_need_queue(pcb_t pcb, process_siginfo_t *siginfo, int type)
{
    int signo = siginfo->ksiginfo.si_signo;
    process_sigqueue_t pending = NULL;

    if (TO_PROCESS == type)
    {
        /* 检查是否有共享的信号需要处理 */
        struct ttos_signal *signal = get_process_signal(pcb);
        if (!sigqueue_isempty(&signal->sig_queue))
        {
            pending = &signal->sig_queue;
        }
    }
    else
    {
        /* 首先检查当前任务是否有pending的信号需要处理 */
        if (!sigqueue_isempty(PRIVATE_SIGQ(pcb)))
        {
            pending = PRIVATE_SIGQ(pcb);
        }
    }

    /* 非实时已经pending的信号,直接丢弃 */
    if (!is_rt_signo(signo) && pending && _sigismember(&pending->sigset_pending, signo))
    {
        /* 丢弃信号*/
        return false;
    }
    else
    {
        /* 排队信号*/
        return true;
    }
}

static pcb_t signal_deliver(pcb_t pcb, process_siginfo_t *siginfo, int flag)
{
    irq_flags_t irq_flag = 0;
    pcb_t dest_pcb = pcb;
    struct sighand_struct *sighand = NULL;
    
#if 0
	/* 目前所有信号均做排队处理，不丢弃信号 */
    /* 检查信号是否需要排队处理,已经pending的非实时信号直接丢弃 */
    if (!signal_need_queue(pcb, siginfo, flag))
    {
        siginfo_delete(siginfo);
        return NULL;
    }
#endif

    /* 投递信号到进程 */
    if (TO_PROCESS == flag)
    {
        /* 查找一个需要被通知的线程,来处理该信号 */
        dest_pcb = _signal_find_catcher(pcb, siginfo->ksiginfo.si_signo);

        struct ttos_signal *signal = get_process_signal(dest_pcb);
        sighand = get_process_sighand(dest_pcb);

        spin_lock_irqsave(&sighand->siglock, irq_flag);

        /* 排队信号 */
        sigqueue_enqueue(&signal->sig_queue, siginfo);

        spin_unlock_irqrestore(&sighand->siglock, irq_flag);
    }
    else if (TO_THREAD == flag) /* 投递信号到指定线程 */
    {
        sighand = get_process_sighand(pcb);

        spin_lock_irqsave(&sighand->siglock, irq_flag);

        /* 排队信号 */
        sigqueue_enqueue(PRIVATE_SIGQ(pcb), siginfo);

        spin_unlock_irqrestore(&sighand->siglock, irq_flag);
    }
    else
    {
        KLOG_E("fail at %s:%d", __FILE__, __LINE__);
    }

    KLOG_D("will wake pcb:%p", dest_pcb);

    do_signal_notify(dest_pcb, siginfo);
    return dest_pcb;
}

int process_sigismember(process_sigset_t *set, int _sig)
{
    unsigned long sig = _sig - 1;

    if (sig > SIGNAL_MAX)
    {
        return 0;
    }

    if (_PROCESS_NSIG_WORDS == 1)
    {
        return 1 & (set->sig[0] >> sig);
    }
    else
    {
        return 1 & (set->sig[sig / _PROCESS_NSIG_BPW] >> (sig % _PROCESS_NSIG_BPW));
    }
}

bool _is_jobctl_signal(pcb_t pcb, int signo)
{
    process_sigset_t jobctl_sigset = process_sigset_init(PROCESS_SIG_JOBCTL_SET);

    return process_sigismember(&jobctl_sigset, signo);
}

int _next_signal(process_sigset_t *pending, process_sigset_t *mask)
{
    unsigned long i, *s, *m, x;
    int sig = 0;

    s = pending->sig;
    m = mask->sig;

    /* 检查第一个long中是否存在signo */
    x = *s & ~*m;
    if (x)
    {
        sig = ttos_ffz(~x) + 1;
        return sig;
    }

    switch (_PROCESS_NSIG_WORDS)
    {
    default:
        for (i = 1; i < _PROCESS_NSIG_WORDS; ++i)
        {
            x = *++s & ~*++m;
            if (!x)
                continue;
            sig = ttos_ffz(~x) + i * _PROCESS_NSIG_BPW + 1;
            break;
        }
        break;

    case 2:
        x = s[1] & ~m[1];
        if (!x)
            break;
        sig = ttos_ffz(~x) + _PROCESS_NSIG_BPW + 1;
        break;

    case 1:
        /* Nothing to do */
        break;
    }

    return sig;
}

int sigqueue_peek(process_sigqueue_t sigqueue, process_sigset_t *mask)
{
    return _next_signal(&sigqueue->sigset_pending, mask);
}

void _sigdelset(process_sigset_t *set, int _sig)
{
    unsigned long sig = _sig - 1;

    if (_PROCESS_NSIG_WORDS == 1)
    {
        set->sig[0] &= ~(1UL << sig);
    }
    else
    {
        set->sig[sig / _PROCESS_NSIG_BPW] &= ~(1UL << (sig % _PROCESS_NSIG_BPW));
    }
}

static process_siginfo_t *sigqueue_dequeue(process_sigqueue_t sigqueue, int signo)
{
    process_siginfo_t *found;
    process_siginfo_t *candidate;
    process_siginfo_t *next;

    found = NULL;
    bool only_one_node = true;

    list_for_each_entry_safe(candidate, next, &sigqueue->siginfo_list, node)
    {
        if (candidate->ksiginfo.si_signo == signo)
        {
            if (found)
            {
                /* 在链表中，相同signo的节点不只1个 */
                only_one_node = false;
                break;
            }
            else
            {
                /* found first */
                found = candidate;
                list_del(&found->node);
            }
        }
    }

    /* 在链表中，相同signo的节点只有一个 */
    if (found && only_one_node)
    {
        /* 清除pending位 */
        _sigdelset(&sigqueue->sigset_pending, signo);
    }

    return found;
}

static void sigqueue_discard_sigset(process_sigqueue_t sigqueue, process_sigset_t *sigset)
{
    process_siginfo_t *queuing_si;
    process_sigset_t mask;
    int signo;

    /* sigqueue_peek中会再次取反 */
    _signotsets(&mask, sigset);
    while ((signo = sigqueue_peek(sigqueue, &mask)) != 0)
    {
        queuing_si = sigqueue_dequeue(sigqueue, signo);
        siginfo_delete(queuing_si);
    }
}

/* 从信号队列中移除指定信号 */
static void signal_remove_shared(pcb_t pcb, process_sigset_t *remove_sigset)
{
    irq_flags_t irq_flag = 0;
    struct ttos_signal *signal = get_process_signal(pcb);
    struct sighand_struct *sighand = get_process_sighand(pcb);

    spin_lock_irqsave(&sighand->siglock, irq_flag);

    /* 移除共享的信号 */
    sigqueue_discard_sigset(&signal->sig_queue, remove_sigset);

    spin_unlock_irqrestore(&sighand->siglock, irq_flag);
}

static void signal_remove_private(pcb_t pcb, process_sigset_t *remove_sigset)
{
    pcb_t tmp = NULL;

    irq_flags_t irq_flag = 0;

    struct sighand_struct *sighand = get_process_sighand(pcb);

    spin_lock_irqsave(&sighand->siglock, irq_flag);

    /* 移除私有的信号 */
    for_each_thread_in_tgroup(pcb, tmp)
    {
        sigqueue_discard_sigset(&tmp->sig_queue, remove_sigset);
    }

    spin_unlock_irqrestore(&sighand->siglock, irq_flag);
}

void signal_cpu_notify(unsigned int cpu_index)
{
    if (cpu_index != CPU_NONE && cpu_index != ttosGetRunningTask()->smpInfo.cpuIndex)
    {
        ttos_pic_ipi_send(GENERAL_IPI_SCHED, cpu_index, 0);
    }
}

static bool sig_valid(int sig)
{
    return (sig >= 0 && sig <= SIGNAL_MAX);
}

static bool sig_should_be_delivered(int sig, pcb_t pcb)
{
    pcb_t tmp_pcb = NULL;
    process_sigset_t remove_sigset = {0};

    /* 信号属于stop信号集 */
    if (is_stop_set(sig))
    {
        /* 移除pcb所有信号队列中的SIGCONT信号 */
        remove_sigset = process_sigset_init(_USIGNAL_SIGMASK(SIGCONT));
        signal_remove_shared(pcb, &remove_sigset);
        signal_remove_private(pcb, &remove_sigset);
    }
    else if (SIGCONT == sig)
    {
        pcb->jobctl_stopped = false;

        /* 移除pcb所有信号队列中的stop信号集 */
        remove_sigset = process_sigset_init(PROCESS_SIG_STOP_SET);
        signal_remove_shared(pcb, &remove_sigset);

        /* 唤醒线程组中的所有线程 */
        for_each_thread_in_tgroup(pcb, tmp_pcb)
        {
            /* 进程未被ptrace，且之前被stop信号停止,则唤醒相应线程 */
            if (!tmp_pcb->group_leader->ptrace &&
                (TTOS_TASK_STOPPED_BY_SIGNAL & tmp_pcb->taskControlId->state))
            {
                /* 唤醒该pcb */
                TTOS_SignalResumeTask(tmp_pcb->taskControlId);
            }
            else
            {
                TTOS_SignalResumeTask(tmp_pcb->taskControlId);
                /* 进程被ptrace，则ptrace通知父进程 */
                // ptrace_trap_notify(tmp_pcb);
            }
        }
    }

    /* 检测信号是否是被忽略的 */
    return !sig_ignored(pcb, sig);
}

static int signal_kill(pcb_t pcb, long signo, long code, ksiginfo_t *kinfo, int flag)
{
    int ret = 0;
    pcb_t dest_pcb = NULL;
    process_siginfo_t *siginfo = NULL;
    unsigned int cpu_index = CPU_NONE;

    /* 进程处于僵尸态 */
    if (pcb == NULL || pcb->exit_state == EXIT_ZOMBIE)
    {
        return -ESRCH;
    }

    /* 发送signo为0的信号目的是为了检测进程是否还存在(在当前接口中,pcb一定是有效的) */
    if (0 == signo)
    {
        /* 返回0,表示进程存在 */
        return 0;
    }

    /* 检测信号值是否有效 */
    if (!sig_valid(signo))
    {
        return -EINVAL;
    }

    /* 检测信号是否应该被投递 */
    if (!sig_should_be_delivered(signo, pcb))
    {
        return -EINVAL;
    }

    /* 创建信号信息结构 */
    siginfo = siginfo_create(signo, code, kinfo);
    if (siginfo)
    {
        /* 投递信号,返回dest_pcb */
        dest_pcb = signal_deliver(pcb, siginfo, flag);
    }
    else
    {
        KLOG_E("%s: siginfo malloc failed", __func__);
        ret = -ENOMEM;
    }

    if (dest_pcb)
    {
        cpu_index = dest_pcb->taskControlId->smpInfo.cpuIndex;
        signal_cpu_notify(cpu_index);
    }

    return ret;
}

static int do_process_signal_kill(pid_t pid, long signo, long si_code, ksiginfo_t *kinfo)
{
    int ret = 0;
    pcb_t pcb;

    pcb = pcb_get_by_pid(pid);

    if (pcb && 0 == signo)
    {
        return 0;
    }

    if (pcb && pcb_get(pcb))
    {
        ret = signal_kill(pcb, signo, si_code, kinfo, TO_PROCESS);
        pcb_put(pcb);
        return ret;
    }

    return -ESRCH;
}

static int do_kernel_tkill(int tid, int signo, long si_code, ksiginfo_t *kinfo)
{
    int ret = 0;
    long flags = 0;
    T_TTOS_TaskControlBlock *thread;

    thread = task_get_by_tid(tid);

    if (thread && 0 == signo)
    {
        return 0;
    }

    if (thread && thread->ppcb && pcb_get(thread->ppcb))
    {
        ret = signal_kill(thread->ppcb, signo, si_code, kinfo, TO_THREAD);
        pcb_put(thread->ppcb);
        return ret;
    }

    return -ESRCH;
}

static int do_pgroup_signal_kill(pid_t pgid, long signo, long si_code, ksiginfo_t *kinfo)
{
    int ret = 0;
    pgroup_t pgrp;
    pcb_t pcb;

    tasklist_lock();

    pgrp = process_pgrp_find(pgid);

    if (pgrp && 0 == signo)
    {
        tasklist_unlock();
        return 0;
    }

    if (pgrp == NULL)
    {
        tasklist_unlock();
        return -ESRCH;
    }

    if (pgrp)
    {
        TTOS_ObtainMutex(pgrp->lock, TTOS_WAIT_FOREVER);
        list_for_each_entry(pcb, &pgrp->process, pgrp_node)
        {
            pid_t pid = get_process_pid(pcb);
            do_process_signal_kill(pid, signo, si_code, kinfo);
        }
        TTOS_ReleaseMutex(pgrp->lock);
    }

    tasklist_unlock();
    return ret;
}

static int do_kernel_signal_kill(pid_t pid, int to, long signo, long code, ksiginfo_t *kinfo)
{
    int ret;
    switch (to)
    {
    case TO_PROCESS:
        ret = do_process_signal_kill(pid, signo, code, kinfo);
        break;
    case TO_THREAD:
        ret = do_kernel_tkill(pid, signo, code, kinfo);
        break;
    case TO_PGROUP:
        ret = do_pgroup_signal_kill(pid, signo, code, kinfo);
        break;
    }
    return ret;
}

static void do_kernel_kill_work(void *arg)
{
    struct signal_work_param *param = (struct signal_work_param *)arg;
    do_kernel_signal_kill(param->pid, param->to, param->signal, param->si_code, param->kinfo);
    free(param);
}

int kernel_signal_kill_with_worker(pid_t pid, int to, long signo, long code, ksiginfo_t *kinfo)
{
    struct signal_work_param *param = malloc(sizeof(struct signal_work_param));
    if (param == NULL)
    {
        KLOG_E("malloc failed at %s %d", __func__, __LINE__);
        return -ENOMEM;
    }
    param->pid = pid;
    param->to = to;
    param->signal = signo;
    param->si_code = code;
    param->kinfo = kinfo;

    work_queue(&param->work, (worker_t)do_kernel_kill_work, param, 0, work_queue_get_highpri());

    /* 在中断中调用的话 一定不是用户态主动调的 不用考虑获取返回值 */
    return 0;
}

int kernel_signal_kill(pid_t pid, int to, long signo, long code, ksiginfo_t *kinfo)
{
    int ret = -ESRCH;

    /* 如果在中断/idle中 则使用worker发送 */
    if (ttosIsISR() || ttosIsIdleTask(ttosGetRunningTask()))
    {
        return kernel_signal_kill_with_worker(pid, to, signo, code, kinfo);
    }

    ret = do_kernel_signal_kill(pid, to, signo, code, kinfo);

    return ret;
}

static void _thread_signal_mask(pcb_t pcb, process_sig_mask_cmd_t how,
                                const process_sigset_t *sigset, process_sigset_t *oset)
{
    process_sigset_t new_set;

    /**
     * Note: POSIX wants this API to be capable to query the current mask
     *       by passing NULL in `sigset`
     */

    /* 如果需要记录任务的的信号屏蔽码，则拷贝 */
    if (oset)
        memcpy(oset, &pcb->blocked, sizeof(process_sigset_t));

    if (sigset)
    {
        _sig_mask_fn[how](pcb, sigset, &new_set);

        /* 从集合中移除不可屏蔽的信号 */
        _sigdelset(&new_set, SIGKILL);
        _sigdelset(&new_set, SIGSTOP);

        /* 重新设置任务的信号屏蔽码 */
        memcpy(&pcb->blocked, &new_set, sizeof(process_sigset_t));
    }
}

void thread_signal_mask_get(pcb_t pcb, process_sigset_t *oset)
{
    _thread_signal_mask(pcb, PROCESS_SIG_MASK_CMD_BLOCK, NULL, oset);
}

int process_thread_signal_mask(pcb_t pcb, process_sig_mask_cmd_t how,
                               const process_sigset_t *sigset, process_sigset_t *oset)
{
    int ret = -1;
    long flag = 0;

    if (pcb)
    {
        sighand_lock(pcb, &flag);

        if (!pcb)
        {
            ret = -EPERM;
        }
        else
        {
            ret = 0;
            _thread_signal_mask(pcb, how, sigset, oset);
        }

       sighand_unlock(pcb, &flag);
    }
    else
    {
        ret = -EINVAL;
    }

    return ret;
}

bool _sighandler_cannot_caught(int signo)
{
    return signo == SIGKILL || signo == SIGSTOP;
}

void signal_del_running(int signo)
{
    if (signo <= 0 || signo >= SIGNAL_MAX)
    {
        return;
    }

    pcb_t pcb = ttosProcessSelf()->group_leader;
    process_sigset_t *running = &pcb->sig_queue.sigset_running;

    _sigdelset(running, signo);
}

static void nocldstop_and_nocldwait_set(int signo, struct ttos_signal *signal,
                                        const struct process_sigaction *act)
{
    long flags = act->sa_flags;

    if (signo == SIGCHLD)
    {
        /* 有该标志，子进程停止或停止后继续运行就不会产生SIGCHLD信号 */
        if (flags & SA_NOCLDSTOP)
        {
            _sigaddset(&signal->nocldstop_set, signo);
        }

        /* 有该标志，子进程退出就不会产生僵尸进程 */
        if (flags & SA_NOCLDWAIT)
        {
            _sigaddset(&signal->nocldwait_set, signo);
        }
    }
}

static void sigqueue_discard(process_sigqueue_t sigqueue, int signo)
{
    process_siginfo_t *queuing_si;
    while (sigqueue_ismember(sigqueue, signo))
    {
        queuing_si = sigqueue_dequeue(sigqueue, signo);
        siginfo_delete(queuing_si);
    }
}

static void flush_sigqueue_mask(process_sigset_t *mask, struct process_sigqueue *s)
{
    process_siginfo_t *q;
    process_siginfo_t *n;
    process_sigset_t m;

    _sigandsets(&m, mask, &s->sigset_pending);
    if (_sigisemptyset(&m))
        return;

    _sigandsets(&s->sigset_pending, &s->sigset_pending, mask);

    list_for_each_entry_safe(q, n, &s->siginfo_list, node)
    {
        if (_sigismember(mask, q->ksiginfo.si_signo))
        {
            list_del(&q->node);
            free(q);
        }
    }
}

static void sigaction_copy(struct sigaction *dest, const struct process_sigaction *src)
{
    dest->__sa_handler.sa_handler = src->__sa_handler._sa_handler;
    dest->sa_restorer = src->sa_restorer;
    /* 记录用户期望在执行handler期间要屏蔽的信号集合 */
    memcpy(&dest->sa_mask, &src->sa_mask, sizeof(sigset_t));
    dest->sa_flags = src->sa_flags;
}

static bool sig_handler_ignored(void *handler, int sig)
{
    process_sigset_t ign_set = process_sigset_init(PROCESS_SIG_IGNORE_SET);

    return handler == SIG_IGN || (handler == SIG_DFL && _sigismember(&ign_set, sig));
}

int process_signal_action(pcb_t pcb, int signo, const struct process_sigaction *restrict act,
                          struct process_sigaction *restrict oact)
{
    pcb_t tmp_pcb = NULL;
    irq_flags_t irq_flag = 0;
    struct ttos_signal *signal = NULL;
    struct sighand_struct *sighand = NULL;
    process_sigset_t remove_sigset = {0};

    assert(!!pcb);

    if (signo < 1 || signo > _NSIG || (act && _sighandler_cannot_caught(signo)))
    {
        return -EINVAL;
    }

    signal = get_process_signal(pcb);
    sighand = get_process_sighand(pcb);

    spin_lock_irqsave(&sighand->siglock, irq_flag);

    if (oact)
    {
        memcpy(oact, &sighand->sig_action[signo - 1], sizeof(struct process_sigaction));
    }

    if (act)
    {
        _sigdelset((process_sigset_t *)&act->sa_mask, SIGKILL);
        _sigdelset((process_sigset_t *)&act->sa_mask, SIGSTOP);

        sigaction_copy(&sighand->sig_action[signo - 1], act);

        spin_unlock_irqrestore(&sighand->siglock, irq_flag);

        /* SA_NOCLDSTOP和SA_NOCLDWAIT 特性的处理 */
        nocldstop_and_nocldwait_set(signo, signal, act);

        /**
         * Brief: Discard the pending signal if signal action is set to SIG_IGN
         *
         * Note: POSIX.1-2017: Setting a signal action to SIG_IGN for a signal
         * that is pending shall cause the pending signal to be discarded,
         * whether or not it is blocked.
         */
        if (sig_handler_ignored(get_sighandler(pcb, signo), signo))
        {
            remove_sigset = process_sigset_init(_USIGNAL_SIGMASK(signo));
            signal_remove_shared(pcb, &remove_sigset);

            tg_lock(pcb->group_leader);

            for_each_thread_in_tgroup(pcb, tmp_pcb)
            {
                signal_remove_private(tmp_pcb, &remove_sigset);
            }

            tg_unlock(pcb->group_leader);
        }
    }
    else
    {
        spin_unlock_irqrestore(&sighand->siglock, irq_flag);
    }

    return 0;
}

void siginfo_k2u(process_siginfo_t *ksigi, siginfo_t *usigi)
{
    memcpy(usigi, &ksigi->ksiginfo, sizeof(*usigi));
}

static int signal_pending_check(pcb_t pcb, process_sigset_t *mask, siginfo_t *usi, bool drop_signal)
{
    int signo = 0;
    irq_flags_t irq_flag = 0;
    process_siginfo_t *si;
    process_sigset_t *pending;
    process_sigqueue_t sigqueue;

    sighand_lock(pcb, &irq_flag);

    sigqueue = PRIVATE_SIGQ(pcb);
    pending = &sigqueue->sigset_pending;
    signo = _next_signal(pending, mask);
    if (!signo)
    {
        struct ttos_signal *signal = get_process_signal(pcb);
        sigqueue = &signal->sig_queue;
        pending = &sigqueue->sigset_pending;
        signo = _next_signal(pending, mask);
    }

    if (signo && drop_signal)
    {
        si = sigqueue_dequeue(sigqueue, signo);
        sighand_unlock(pcb, &irq_flag);
        siginfo_k2u(si, usi);
        siginfo_delete(si);
    }
    else
    {
        sighand_unlock(pcb, &irq_flag);
    }

    return signo;
}

static int _search_signal(process_sigset_t *pending, long signo)
{
    unsigned long sig = signo - 1;
    unsigned long found = 0;

    if (_PROCESS_NSIG_WORDS == 1)
    {
        found = (pending->sig[0] & (1UL << sig));
    }
    else
    {
        found = pending->sig[sig / _PROCESS_NSIG_BPW] & (1UL << (sig % _PROCESS_NSIG_BPW));
    }

    if (found)
    {
        return 0;
    }

    return -1;
}

int signal_in_pending(pcb_t pcb, long signo, int flag)
{
    int ret = -1;
    process_sigqueue_t pending = NULL;

    if (signo < 0 || signo >= SIGNAL_MAX)
    {
        return -1;
    }

    if (flag == TO_PROCESS)
    {
        struct ttos_signal *signal = get_process_signal(pcb);
        if (!sigqueue_isempty(&signal->sig_queue))
        {
            pending = &signal->sig_queue;
        }
    }
    else if (flag == TO_THREAD)
    {
        if (!sigqueue_isempty(PRIVATE_SIGQ(pcb)))
        {
            pending = PRIVATE_SIGQ(pcb);
        }
    }

    if (pending)
    {
        ret = _search_signal(&pending->sigset_pending, signo);
    }

    return ret;
}

int signal_in_running(pcb_t pcb, long signo)
{
    int ret;
    process_sigset_t running;

    running = pcb->sig_queue.sigset_running;
    ret = _search_signal(&running, signo);

    return ret;
}

int clockTimespecToTicks(const struct timespec *time)
{
    int tick = 0;
    int tick_ns = 0;
    unsigned int sys_clk_rate = TTOS_GetSysClkRate();
    tick_ns = time->tv_nsec / (NSEC_PER_SEC / sys_clk_rate);
    tick = time->tv_sec * sys_clk_rate + tick_ns;
    if (tick_ns * (NSEC_PER_SEC / sys_clk_rate) < time->tv_nsec)
    {
        tick++;
    }
    if (tick < 0)
    {
        tick = 0;
    }
    return tick;
}

int kernel_signal_timedwait(T_TTOS_TaskControlBlock *task, process_sigset_t *sigset,
                            struct timespec *timeout, bool drop_signal)
{
    int ret = 0;
    int ticks = TTOS_WAIT_FOREVER;
    process_sigset_t saved_sigset;
    process_sigset_t blocked_sigset;
    process_sigset_t dontwait_sigset;
    int sig;
    pcb_t pcb = task->ppcb;
    irq_flags_t msr = 0;
    irq_flags_t flag = 0;


    if (0 == pcb)
    {
        assert(0);
    }

    /**
     * Brief: POSIX
     * If one of the signals in set is already pending for the calling task,
     * sigwaitinfo() will return immediately
     */

    /* Create a mask of signals user dont want or cannot catch */
    _sigdelset(sigset, SIGKILL);
    _sigdelset(sigset, SIGSTOP);
    _signotsets(&dontwait_sigset, sigset);

    /* 检查将要等待的信号是否已经可获取 */
    sig = signal_pending_check(pcb, &dontwait_sigset, &task->wait.sig_info, drop_signal);

    /* 等待的信号已经pending,则返回 */
    if (sig)
    {
        return sig;
    }

    /**
     * Brief: POSIX
     * if none of the signals specified by set are pending, sigtimedwait() shall
     * wait for the time interval specified in the timespec structure referenced
     * by timeout.
     *
     * Note: If the pending signal arrives before task suspend, the suspend
     * operation will return a failure
     */

    sighand_lock(pcb, &flag);

    /* 计算出任务屏蔽的信号集 */
    _sigandsets(&blocked_sigset, &pcb->blocked, sigset);

    /* 保存被屏蔽的信号 */
    _thread_signal_mask(pcb, PROCESS_SIG_MASK_CMD_SET_MASK, &blocked_sigset, &saved_sigset);

    pcb->real_blocked = saved_sigset;

    sighand_unlock(pcb, &flag);

    ttosDisableTaskDispatchWithLock();

    ttos_int_lock(msr);
    if (timeout)
    {
        ticks = clockTimespecToTicks(timeout);

        /**
         * Brief: POSIX
         * If the timespec structure pointed to by timeout is zero-valued and
         * if none of the signals specified by set are pending, then
         * sigtimedwait() shall return immediately with an error
         */

        /* 超时时间转换 */
        if (ticks == 0)
        {
            KLOG_I("%s time too short at line:%d", __func__, __LINE__);
            ttos_int_unlock(msr);
            ttosEnableTaskDispatchWithLock();
            return -EAGAIN;
        }
    }

    /* 等待对象信息 */
    task->wait.returnCode = TTOS_TIMEOUT;

    /* 将当前任务插入到信号的接收等待队列中 */
    ttosEnqueueTaskq(&signalsWaitPendingQueue, ticks, TRUE);

    ttos_int_unlock(msr);
    ttosEnableTaskDispatchWithLock();

    ret = task->wait.returnCode;
    if (TTOS_TIMEOUT == ret)
    {
        ret = -EAGAIN;
    }

    sighand_lock(pcb, &flag);

    pcb->blocked = pcb->real_blocked;
    memset(&pcb->real_blocked, 0, sizeof(pcb->real_blocked));

    sighand_unlock(pcb, &flag);

    sig = signal_pending_check(pcb, &dontwait_sigset, &task->wait.sig_info, drop_signal);


    return sig ? sig : ret;
}

int kernel_sigtimedwait(const sigset_t *sigset, siginfo_t *info, const struct timespec *timeout,
                        size_t sigsize)
{
    int sig;
    T_TTOS_TaskControlBlock *task = ttosGetRunningTask();
    process_sigset_t process_set;
    struct timespec ktimeout;
    struct timespec *ptimeout;

    /* Fit sigset size to process set */
    if (sizeof(process_set) < sigsize)
    {
        KLOG_E("%s: sigsize (%" PRIxPTR ") extends process sigset chunk", __func__, sigsize);
        sigsize = sizeof(process_set);
    }
    else
    {
        /* if sigset of user is smaller, clear extra space */
        memset(&process_set, 0, sizeof(process_set));
    }

    copy_from_user(&process_set, (void *)sigset, sigsize);

    if (timeout)
    {
        copy_from_user(&ktimeout, (void *)timeout, sizeof(*timeout));
        ptimeout = &ktimeout;
    }
    else
    {
        ptimeout = NULL;
    }

    sig = kernel_signal_timedwait(task, &process_set, ptimeout, true);

    if (sig > 0 && info)
    {
        info->si_signo = task->wait.sig_info.si_signo;
        info->si_code = task->wait.sig_info.si_code;
        info->si_value.sival_int = task->wait.sig_info.si_value.sival_int;
    }

        return sig;
}

void process_sigqueue_init(process_sigqueue_t sigq)
{
    memset(&sigq->sigset_pending, 0, sizeof(process_sigset_t));
    memset(&sigq->sigset_running, 0, sizeof(process_sigset_t));
    INIT_LIST_HEAD(&sigq->siginfo_list);
}

int process_signal_init(struct ttos_signal *sig)
{
    process_sigqueue_init(&sig->sig_queue);
    return 0;
}

bool is_stop_set(int signo)
{
    process_sigset_t stop_sigset = process_sigset_init(PROCESS_SIG_STOP_SET);

    return process_sigismember(&stop_sigset, signo);
}

bool _is_coredump_signal(int signo)
{
    process_sigset_t coredump_sigset = process_sigset_init(PROCESS_SIG_COREDUMP_SET);

    return process_sigismember(&coredump_sigset, signo);
}

bool _need_notify_status_changed(pcb_t pcb, int signo)
{
    struct ttos_signal *signal = get_process_signal(pcb);
    assert(process_sigismember(&process_sigset_init(PROCESS_SIG_JOBCTL_SET), signo));
    return !process_sigismember(&signal->nocldstop_set, SIGCHLD);
}

static void _notify_parent_and_leader(pcb_t child_process, int signo, bool is_stop)
{
    int si_code;
    ksiginfo_t *info;
    T_TTOS_TaskControlBlock *child_thread = child_process->taskControlId;
    pcb_t parent_process = child_process->group_leader->parent;

    if (!parent_process)
    {
        return;
    }

    /* prepare the event data for parent to query */
    if (is_stop)
    {
        si_code = CLD_STOPPED;
        child_process->state = PROCESS_CREATE_STAT_STOPPED(signo);
    }
    else
    {
        si_code = CLD_CONTINUED;
        child_process->state = PROCESS_CREATE_STAT_CONTINUED;
    }

    /* 唤醒waitpid上的任务 */
    process_wakeup_waiter(child_process);

    /*
        检测是否需要通知父进程状态改变,根据SIGCHLD 对应handler 的 SA_NOCLDSTOP标志。
        如果设置了SA_NOCLDSTOP，则表示子进程stop或者stop后continue 不通知父进程状态改变,
        即不发SIGCHLD。否则，会发SIGCHLD通知父进程状态改变。
    */
    if (_need_notify_status_changed(parent_process, signo))
    {
        child_process->exit_signal = signo;

        ksiginfo_t info = {0};
        info.si_status = signo;
        pid_t pid = get_process_pid(parent_process);

        /* 给父进程发SIGCHLD信号,通知状态变化 */
        kernel_signal_kill(pid, TO_PROCESS, SIGCHLD, si_code, &info);
    }
}

static int stop_process(pcb_t pcb, int signo, process_siginfo_t *si, process_sigqueue_t sq)
{
    int error = 0;
    pcb_t tmp_pcb = NULL;
    T_TTOS_TaskControlBlock *cur_task = NULL;
    int jobctl_stopped = pcb->jobctl_stopped;

    cur_task = ttosGetRunningTask();

    tg_lock(pcb->group_leader);

    /* 收到stop信号，需要停止线程组中的所有线程 */
    for_each_thread_in_tgroup(pcb, tmp_pcb)
    {
        /* 给除了自己以外非TTOS_TASK_STOPPED_BY_SIGNAL状态的任务发SIGSTOP信号 */
        if (tmp_pcb != pcb && !(TTOS_TASK_STOPPED_BY_SIGNAL & tmp_pcb->taskControlId->state))
        {
            tmp_pcb->taskControlId->state |= TTOS_TASK_STOPPED_BY_SIGNAL;
            kernel_signal_kill(tmp_pcb->taskControlId->tid, TO_THREAD, SIGSTOP, SI_KERNEL, NULL);
        }
    }

    errno = (0);

    pcb->taskControlId->state |= TTOS_TASK_STOPPED_BY_SIGNAL;
    tg_unlock(pcb->group_leader);
    /* After suspension, only the SIGKILL and SIGCONT will wake this thread up */
    error = TTOS_SignalSuspendTask(pcb->taskControlId);
    if (error == 0)
    {
        error = errno;
        error = error > 0 ? -error : error;
    }

    /* 代码运行到此，表示收到SIGCONT信号被唤醒 */
    if ((sigqueue_ismember(PRIVATE_SIGQ(pcb), SIGCONT) ||
         sigqueue_ismember(&((struct ttos_signal *)get_process_signal(pcb))->sig_queue, SIGCONT)))
    {
        /* 因为收到了SIGCONT信号被唤醒,通知父进程 */
        _notify_parent_and_leader(pcb, SIGCONT, false);
    }

    return error;
}

void do_work_pending(void *exp_frame)
{
    /* 调度检查,需要调度则调度 */

    /* pending信号的处理 */
    arch_do_signal(exp_frame);
}

int sigqueue_examine(process_sigqueue_t sigqueue, process_sigset_t *pending)
{
    int is_empty = sigqueue_isempty(sigqueue);
    if (!is_empty)
    {
        _sigorsets(pending, &sigqueue->sigset_pending, &sigqueue->sigset_pending);
    }
    return is_empty;
}

void process_thread_signal_pending(pcb_t pcb, process_sigset_t *pending)
{
    long flag;

    if (pcb)
    {
        struct ttos_signal *signal = get_process_signal(pcb);
        memset(pending, 0, sizeof(process_sigset_t));

        spin_lock_irqsave(&signal->siglock, flag);

        sigqueue_examine(PRIVATE_SIGQ(pcb), pending);
        sigqueue_examine(&signal->sig_queue, pending);

        _sigandsets(pending, pending, &pcb->blocked);

        spin_unlock_irqrestore(&signal->siglock, flag);
    }
}

bool pending_signal_exist(void)
{
    process_sigset_t pending;
    process_sigset_t pending_null;

    memset(&pending, 0, sizeof(process_sigset_t));
    memset(&pending_null, 0, sizeof(process_sigset_t));

    process_thread_signal_pending(ttosProcessSelf(), &pending);

    if (memcmp(&pending_null, &pending, sizeof(process_sigset_t)))
    {
        return true;
    }
    else
    {
        return false;
    }
}

bool task_signal_available(void)
{
    pcb_t pcb = ttosProcessSelf();

    if (signal_available())
    {
        pcb->taskControlId->wait.returnCode = TTOS_SIGNAL_INTR;
        return true;
    }
    else
    {
        return false;
    }
}

/* 该函数临时替换任务的信号屏蔽码,然后去等待与除该信号集外的所有信号,等到某一信号后，最后再恢复原始的信号屏蔽集
 */
void sigsuspend(const sigset_t *set)
{
    int ret = 0;
    irq_flags_t flag = 0;
    pcb_t pcb = ttosProcessSelf();
    pcb->saved_sigmask = pcb->blocked;

    ret = process_thread_signal_mask(pcb, PROCESS_SIG_MASK_CMD_SET_MASK, (process_sigset_t *)set,
                                     NULL);
    if (ret)
    {
        KLOG_E("fail at %s:%d\n", __FILE__, __LINE__);
        return;
    }

    /* 检查等待的信号是否已经存在 */
    int sig = signal_pending_check(pcb, &pcb->blocked, NULL, false);

    /* 等待的信号已经pending,则返回 */
    if (sig)
    {
        return;
    }

    ttosDisableTaskDispatchWithLock();
    ttos_int_lock(flag);

    /* 将当前任务插入到信号的接收等待队列中 */
    ttosEnqueueTaskq(&signalsWaitQueue, TTOS_WAIT_FOREVER, TRUE);

     ttos_int_unlock(flag);
    ttosEnableTaskDispatchWithLock();

    ttosProcessSelf()->need_restore_blocked = true;
}

pid_t process_pgid_get_byprocess(pcb_t process)
{
    return process ? process->pgid : 0;
}

static void sighand_obj_destroy(void *args)
{
    free(args);
}

struct process_obj *sighand_obj_alloc(pcb_t pcb)
{
    struct sighand_struct *sighandl;

    sighandl = calloc(1, sizeof(struct sighand_struct));

    spin_lock_init(&sighandl->siglock);

    return process_obj_create(pcb, sighandl, sighand_obj_destroy);
}

int sighand_obj_ref(pcb_t parent, pcb_t child)
{
    child->sighand = process_obj_ref(child, parent->sighand);
    return 0;
}

static void signal_obj_destroy(void *args)
{
    struct ttos_signal *signal = (struct ttos_signal *)args;
    timer_event_stop(&signal->real_timer);
    free(args);
}

struct process_obj *signal_obj_alloc(pcb_t pcb)
{
    struct ttos_signal *signal;

    signal = calloc(1, sizeof(struct ttos_signal));

    spin_lock_init(&signal->siglock);

    process_sigqueue_init(&signal->sig_queue);

    return process_obj_create(pcb, signal, signal_obj_destroy);
}

int signal_obj_ref(pcb_t parent, pcb_t child)
{
    child->signal = process_obj_ref(child, parent->signal);
    return 0;
}

int copy_sighand(unsigned long clone_flags, pcb_t child)
{
    irq_flags_t irq_flag = 0;
    struct sighand_struct *sighand;
    pcb_t cur_pcb = ttosProcessSelf();
    struct sighand_struct *cur_sighand;
    struct sighand_struct tmp_sighand;

    if (cur_pcb)
    {
        cur_sighand = get_process_sighand(cur_pcb);
    }
    else
    {
        memset(&tmp_sighand, 0, sizeof(struct sighand_struct));
        cur_sighand = &tmp_sighand;
    }

    if (clone_flags & CLONE_SIGHAND)
    {
        if (cur_pcb)
        {
            sighand_obj_ref(cur_pcb, child);
        }
        else
        {
            KLOG_E("fail at %s:%d", __FILE__, __LINE__);
            return -1;
        }

        return 0;
    }

    child->sighand = sighand_obj_alloc(child);
    if (!child->sighand)
    {
        return -ENOMEM;
    }

    sighand = get_process_sighand(child);

    spin_lock_irqsave(&sighand->siglock, irq_flag);
    memcpy(sighand->sig_action, cur_sighand->sig_action, _NSIG * sizeof(struct sigaction));
    spin_unlock_irqrestore(&sighand->siglock, irq_flag);

    return 0;
}

int copy_signal(unsigned long clone_flags, pcb_t child)
{
    struct process_obj *sig;
    struct ttos_signal *src_sig, *new_sig;

    if (clone_flags & CLONE_SIGHAND)
    {
        signal_obj_ref(ttosProcessSelf(), child);
        return 0;
    }

    sig = signal_obj_alloc(child);
    child->signal = sig;
    if (!sig)
        return -ENOMEM;

    new_sig = PROCESS_OBJ_GET(sig, struct ttos_signal *);
    src_sig = get_process_signal(child);

    new_sig->real_timer.priv = child;
    INIT_SPIN_LOCK(&new_sig->real_timer.active_lock);
    INIT_LIST_HEAD(&new_sig->real_timer.active_head);
    list_add_after(&new_sig->real_timer.active_head, &src_sig->real_timer.active_head);
    return 0;
}

int fork_signal(unsigned long clone_flags, pcb_t child)
{
    int ret;

    memset(&child->saved_sigmask, 0, sizeof(process_sigset_t));
    memset(&child->sig_queue, 0, sizeof(struct process_sigqueue));
    INIT_LIST_HEAD(&(child->sig_queue.siginfo_list));

    ret = copy_sighand(clone_flags, child);
    if (ret)
    {
        return ret;
    }

    return copy_signal(clone_flags, child);
}

int signal_reset(pcb_t pcb)
{
    int signo = 0;
    irq_flags_t hand_irq_flag;
    irq_flags_t signal_irq_flag;
    process_sigqueue_t pending;
    process_sigset_t *sig_mask;
    process_siginfo_t *siginfo;
    struct ttos_signal *signal = get_process_signal(pcb);
    struct sighand_struct *sighand = get_process_sighand(pcb);

    spin_lock_irqsave(&sighand->siglock, hand_irq_flag);
    spin_lock_irqsave(&signal->siglock, signal_irq_flag);

    /* 首先检查当前任务是否有pending的信号需要处理 */
    if (!sigqueue_isempty(PRIVATE_SIGQ(pcb)))
    {
        pending = PRIVATE_SIGQ(pcb);
        sig_mask = &pcb->blocked;
        signo = sigqueue_peek(pending, sig_mask);
    }

    /* 检查是否有共享的信号需要处理 */
    if (!signo && !sigqueue_isempty(&signal->sig_queue))
    {
        pending = &signal->sig_queue;
        sig_mask = &pcb->blocked;
        signo = sigqueue_peek(pending, sig_mask);
    }

    if (signo)
    {
        siginfo = sigqueue_dequeue(pending, signo);
        siginfo_delete(siginfo);
    }

    pcb->need_restore_blocked = 0;
    memset(&pcb->blocked, 0, sizeof(pcb->blocked));
    memset(&pcb->real_blocked, 0, sizeof(pcb->real_blocked));
    memset(&pcb->saved_sigmask, 0, sizeof(pcb->saved_sigmask));
    memset(&sighand->sig_action, 0, sizeof(sighand->sig_action));

    spin_unlock_irqrestore(&sighand->siglock, hand_irq_flag);

    INIT_LIST_HEAD(&signal->sig_queue.siginfo_list);
    memset(&signal->real_timer, 0, sizeof(signal->real_timer));
    memset(&signal->sig_queue.sigset_pending, 0, sizeof(signal->sig_queue.sigset_pending));
    memset(&signal->nocldstop_set, 0, sizeof(signal->nocldstop_set));
    memset(&signal->nocldwait_set, 0, sizeof(signal->nocldwait_set));

    INIT_SPIN_LOCK(&signal->real_timer.active_lock);

    spin_unlock_irqrestore(&signal->siglock, signal_irq_flag);

    return 0;
}

int kernel_sigqueueinfo(pid_t pid, int sig, siginfo_t *kinfo)
{
    pcb_t pcb;

    if ((kinfo->si_code >= 0 || kinfo->si_code == SI_TKILL) &&
        (get_process_pid(ttosProcessSelf()) != pid))
    {
        return -EPERM;
    }

    return kernel_signal_kill(pid, TO_PROCESS, sig, kinfo->si_code, kinfo);
}

unsigned long sigsp(unsigned long sp, struct ksignal *ksignal)
{
    pcb_t pcb = ttosProcessSelf();
    if (unlikely((ksignal->ka.sa_flags & SA_ONSTACK)) && !sas_ss_flags(sp))
#ifdef CONFIG_STACK_GROWSUP
        return current->sas_ss_sp;
#else
        return pcb->sas_ss_sp + pcb->sas_ss_size;
#endif
    return sp;
}

int __on_sig_stack(unsigned long sp)
{
    pcb_t pcb = ttosProcessSelf();
#ifdef CONFIG_STACK_GROWSUP
    return sp >= pcb->sas_ss_sp && sp - pcb->sas_ss_sp < pcb->sas_ss_size;
#else
    return sp > pcb->sas_ss_sp && sp - pcb->sas_ss_sp <= pcb->sas_ss_size;
#endif
}

/*
 * True if we are on the alternate signal stack.
 */
int on_sig_stack(unsigned long sp)
{
    pcb_t pcb = ttosProcessSelf();
    /*
     * If the signal stack is SS_AUTODISARM then, by construction, we
     * can't be on the signal stack unless user code deliberately set
     * SS_AUTODISARM when we were already on it.
     *
     * This improves reliability: if user state gets corrupted such that
     * the stack pointer points very close to the end of the signal stack,
     * then this check will enable the signal to be handled anyway.
     */
    if (pcb->sas_ss_flags & SS_AUTODISARM)
        return 0;

    return __on_sig_stack(sp);
}

int sas_ss_flags(unsigned long sp)
{
    pcb_t pcb = ttosProcessSelf();
    if (!pcb->sas_ss_size)
        return SS_DISABLE;

    return on_sig_stack(sp) ? SS_ONSTACK : 0;
}

int do_sigaltstack(const stack_t *ss, stack_t *oss, unsigned long sp, size_t min_ss_size)
{
    pcb_t pcb = ttosProcessSelf();
    int ret = 0;

    if (oss)
    {
        memset(oss, 0, sizeof(stack_t));
        oss->ss_sp = (void __user *)pcb->sas_ss_sp;
        oss->ss_size = pcb->sas_ss_size;
        oss->ss_flags = sas_ss_flags(sp) | (pcb->sas_ss_flags & SS_FLAG_BITS);
    }

    if (ss)
    {
        void __user *ss_sp = ss->ss_sp;
        size_t ss_size = ss->ss_size;
        unsigned ss_flags = ss->ss_flags;
        int ss_mode;

        if (unlikely(on_sig_stack(sp)))
            return -EPERM;

        ss_mode = ss_flags & ~SS_FLAG_BITS;
        if (unlikely(ss_mode != SS_DISABLE && ss_mode != SS_ONSTACK && ss_mode != 0))
            return -EINVAL;

        /*
         * Return before taking any locks if no actual
         * sigaltstack changes were requested.
         */
        if (pcb->sas_ss_sp == (unsigned long)ss_sp && pcb->sas_ss_size == ss_size &&
            pcb->sas_ss_flags == ss_flags)
            return 0;

        // sigaltstack_lock();
        if (ss_mode == SS_DISABLE)
        {
            ss_size = 0;
            ss_sp = NULL;
        }
        else
        {
            if (unlikely(ss_size < min_ss_size))
                ret = -ENOMEM;
        }
        if (!ret)
        {
            pcb->sas_ss_sp = (unsigned long)ss_sp;
            pcb->sas_ss_size = ss_size;
            pcb->sas_ss_flags = ss_flags;
        }

        // sigaltstack_unlock();
    }
    return ret;
}

int restore_altstack(const stack_t __user *uss, unsigned long sp)
{
    stack_t new;

    if (copy_from_user(&new, uss, sizeof(stack_t)))
        return -EFAULT;

    (void)do_sigaltstack(&new, NULL, sp, MINSIGSTKSZ);

    /* squash all but EFAULT for now */
    return 0;
}

void __save_altstack(stack_t __user *uss, unsigned long sp)
{
    pcb_t pcb = ttosProcessSelf();

    uss->ss_sp = (void *)pcb->sas_ss_sp;
    uss->ss_flags = pcb->sas_ss_flags;
    uss->ss_size = pcb->sas_ss_size;
}

process_sigset_t *sigmask_to_save(void)
{
    pcb_t pcb = ttosProcessSelf();
    process_sigset_t *res = &pcb->blocked;

    if (pcb->need_restore_blocked)
    {
        res = &pcb->saved_sigmask;
    }

    return res;
}

void sas_ss_reset(pcb_t pcb)
{
    pcb->sas_ss_sp = 0;
    pcb->sas_ss_size = 0;
    pcb->sas_ss_flags = SS_DISABLE;
}

bool is_thread_group_leader(pcb_t p)
{
    return (p->group_leader == p);
}

bool task_sigpending(void)
{
    process_sigset_t set = {0};
    process_thread_signal_pending(ttosProcessSelf(), &set);

    if (_sigisemptyset(&set))
    {
        return false;
    }
    else
    {
        return true;
    }
}

bool signal_available(void)
{
    process_sigset_t available_sig;
    process_sigset_t unblocked_sig;
    pcb_t pcb = ttosProcessSelf();
    long flag;

    if (pcb)
    {
        struct ttos_signal *signal = get_process_signal(pcb);
        memset(&available_sig, 0, sizeof(process_sigset_t));

        spin_lock_irqsave(&signal->siglock, flag);

        sigqueue_examine(PRIVATE_SIGQ(pcb), &available_sig);
        sigqueue_examine(&signal->sig_queue, &available_sig);

        _signotsets(&unblocked_sig, &pcb->blocked);
        _sigandsets(&available_sig, &available_sig, &unblocked_sig);

        spin_unlock_irqrestore(&signal->siglock, flag);
    }
    else
    {
        return false;
    }

    if (_sigisemptyset(&available_sig))
    {
        return false;
    }
    else
    {
        return true;
    }
}

void signal_delivered(struct ksignal *ksig, int stepping)
{
    process_sigset_t blocked = {0};
    pcb_t pcb = ttosProcessSelf();

    /* A signal was successfully delivered, and the
       saved sigmask was stored on the signal frame,
       and will be restored by sigreturn.  So we can
       simply clear the restore sigmask flag.  */

    // clear_restore_sigmask();
    pcb->need_restore_blocked = false;

    _sigorsets(&blocked, &pcb->blocked, (process_sigset_t *)&ksig->ka.sa_mask);

    /* SA_NODEFER标志决定在调用handler期间是否要mask 该信号 */
    if (!(ksig->ka.sa_flags & SA_NODEFER))
    {
        _sigaddset(&blocked, ksig->sig);
    }

    set_current_blocked(&blocked);

    if (pcb->sas_ss_flags & SS_AUTODISARM)
    {
        sas_ss_reset(pcb);
    }

    if (stepping)
    {
        // ptrace_notify(SIGTRAP, 0);
    }
}

/**
 * set_current_blocked - change current->blocked mask
 * @newset: new mask
 *
 * It is wrong to change ->blocked directly, this helper should be used
 * to ensure the process can't miss a shared signal we are going to block.
 */
void set_current_blocked(process_sigset_t *newset)
{
    _sigdelset(newset, SIGKILL);
    _sigdelset(newset, SIGSTOP);

    pcb_t pcb = ttosProcessSelf();
    pcb->blocked = *newset;

    //__set_current_blocked(newset);
}

void signal_forget_syscall(struct arch_context *context)
{
    context->type = CONTEXT_NONE;
}

int peek_signal(pcb_t pcb, process_sigqueue_t *pending)
{
    int signo = 0;

    process_sigset_t *sig_mask = NULL;

    /* 检查线程是否有pending的信号需要处理 */
    if (!sigqueue_isempty(PRIVATE_SIGQ(pcb)))
    {
        *pending = PRIVATE_SIGQ(pcb);
        sig_mask = &pcb->blocked;
        signo = sigqueue_peek(*pending, sig_mask);
    }

    /* 检查进程是否有信号需要处理 */
    struct ttos_signal *signal = get_process_signal(pcb);
    if (!signo && !sigqueue_isempty(&signal->sig_queue))
    {
        *pending = &signal->sig_queue;
        sig_mask = &pcb->blocked;
        signo = sigqueue_peek(*pending, sig_mask);
    }

    return signo;
}

void do_signal_stop(pcb_t pcb, int signo, process_siginfo_t *siginfo,
                    process_sigqueue_t pending_queue)
{
    /* 进程未被ptrace的stop处理 */
    if (!pcb->group_leader->ptrace)
    {
        /* 通知父进程 */
        _notify_parent_and_leader(pcb, signo, true);

        /* 停止进程 */
        stop_process(pcb, signo, siginfo, pending_queue);
    }
    else
    {
        ptrace_notify(signo);
    }
}

void exit_del(int signo, pcb_t pcb, int coredumped)
{
    int exit_code = pcb->group_leader->group_exit_status.exit_code;

    //如果进程组已经退出
    if (pcb->group_leader->group_exit_status.is_terminated)
    {
        if (pcb->group_leader->group_exit_status.is_normal_exit)
        {
            pcb->exit_code = exit_code;
            //自己退出
            process_exit(pcb);
        }
        else
        {
            /* 记录当前进程是否由coredump退出 */
            pcb->exit_signal = exit_code | (coredumped ? COREDUMP_FLAG : 0);
            //自己退出
            process_exit(pcb);
        }
    }
    else //如果进程组未退出，则退出组
    {
        if (pcb->group_exit_status.group_request_terminate)
        {
            process_exit_group(exit_code, pcb->group_leader->group_exit_status.is_normal_exit);
        }
        else
        {
            /* 记录当前进程是否由coredump退出 */
            signo = signo | (coredumped ? COREDUMP_FLAG : 0);
            process_exit_group(signo, false);
        }
    }
}
static int ptrace_signal(pcb_t pcb, int signr, ksiginfo_t *info)
{
    pcb->group_leader->ptrace_siginfo = *info;
    return 0;
}

bool get_signal(struct ksignal *ksig)
{
    int signr = 0;
    int coredumped = 0;
    struct sigaction *ka = NULL;
    irq_flags_t irq_flag = 0;
    pcb_t pcb = ttosProcessSelf();
    assert(!!pcb || !!pcb->taskControlId);
    process_siginfo_t siginfo = {0};
    process_sigqueue_t pending = NULL;
    process_siginfo_t *siginfo_ptr = NULL;
    struct sighand_struct *sighand = get_process_sighand(pcb);
    struct ttos_signal *signal = get_process_signal(pcb);

    spin_lock_irqsave(&sighand->siglock, irq_flag);

    while (1)
    {
        /* 查找是否有信号需要处理 */
        signr = peek_signal(pcb, &pending);
        if (!signr)
        {
            /* 没有信号需要处理 */
            break;
        }

        /* 信号信息出队 */
        siginfo_ptr = sigqueue_dequeue(pending, signr);

        /* 保存信号信息 */
        siginfo = *siginfo_ptr;

        /* 释放信号信息空间 */
        siginfo_delete(siginfo_ptr);

        /* 记录信号信息，以便ptrace查询 */
        if (unlikely(pcb->ptrace) && (signr != SIGKILL))
        {
            ptrace_signal(pcb, signr, &siginfo.ksiginfo);
        }

        /* 找到信号关联的处理结构 */
        ka = &sighand->sig_action[signr - 1];

        /* 如果信号handler为忽略该信号 */
        if (ka->__sa_handler.sa_handler == SIG_IGN)
        {
            /* 继续检测下一个信号 */
            continue;
        }

        /* 如果信号handler不为默认处理行为 */
        if (ka->__sa_handler.sa_handler != SIG_DFL)
        {
            /* 记录该信号的处理结构 */
            ksig->ka = *ka;

            ksig->info = siginfo.ksiginfo;

            /* 如果该信号有设置SA_ONESHOT标志,则复位该信号为默认行为 */
            if (ka->sa_flags & SA_ONESHOT)
            {
                ka->__sa_handler.sa_handler = SIG_DFL;
            }

            /* 记录当前正在处理的信号 */
            signal_set_running(signr);

            break;
        }

        /*
         * Now we are doing the default action for this signal.
         */
        if (in_ignored_set(signr))
        {
            continue;
        }

        /* 为SIGTRAP或属于停止信号集 */
        if ((pcb->group_leader->ptrace && SIGTRAP == signr) || is_stop_set(signr))
        {
            spin_unlock_irqrestore(&sighand->siglock, irq_flag);
            do_signal_stop(pcb, signr, &siginfo, pending);
            spin_lock_irqsave(&sighand->siglock, irq_flag);
            continue;
        }

        /* coredump处理 */
        if (!pcb->group_leader->ptrace && _is_coredump_signal(signr))
        {
#ifdef CONFIG_COREDUMP
            spin_unlock_irqrestore(&sighand->siglock, irq_flag);
            coredumped = ttos_coredump(signr);
            spin_lock_irqsave(&sighand->siglock, irq_flag);
            extern bool coredump_task_res;
            char *current_task_name = ttosGetRunningTaskName();
            if (coredumped && coredump_task_done())
                KLOG_EMERG("core dumped, error task: %s. saved in /var/corefile/",
                           current_task_name);
            else
                KLOG_EMERG("core dumped, error task: %s. core file generation failed.",
                           current_task_name);
#endif
        }

        if (pcb->group_leader->ptrace && signr != SIGKILL)
        {
            ptrace_notify(signr);
        }
        else
        {
            spin_unlock_irqrestore(&sighand->siglock, irq_flag);

            /* 进程终止处理 */
            exit_del(signr, pcb, coredumped);

            spin_lock_irqsave(&sighand->siglock, irq_flag);
        }
    }

    spin_unlock_irqrestore(&sighand->siglock, irq_flag);

    ksig->sig = signr;
out:
    return signr > 0;
}

bool in_syscall(struct arch_context *context)
{
    return (SYSCALL_CONTEXT == context->type) ? true : false;
}

void restore_saved_sigmask(void)
{
    pcb_t pcb = ttosProcessSelf();

    /* 没有信号需要处理，检查是否有信号屏蔽集需要恢复 */
    if (pcb && pcb->need_restore_blocked)
    {
        pcb->blocked = pcb->saved_sigmask;
        pcb->need_restore_blocked = false;
        memset(&pcb->saved_sigmask, 0, sizeof(process_sigset_t));
    }
}

int copy_siginfo_to_user(siginfo_t __user *to, const siginfo_t *from)
{
    if (copy_to_user(to, from, sizeof(siginfo_t)))
        return -EFAULT;

    return 0;
}
