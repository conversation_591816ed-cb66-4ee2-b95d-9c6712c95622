/****************************************************************************
 * fs/driver/fs_mtdproxy.c
 *
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.  The
 * ASF licenses this file to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the
 * License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 ****************************************************************************/

/****************************************************************************
 * Included Files
 ****************************************************************************/
#ifdef CONFIG_MTD
#include <sys/stat.h>
#include <sys/types.h>

#include <assert.h>
#include <errno.h>
#include <stdio.h>
#include <string.h>

#include <mtd/mtd.h>
#include <stdlib.h>
#include <ttos.h>

#include "../inode/inode.h"
#include <atomic.h>

#define KLOG_TAG "mtdproxy"
#include <klog.h>

/****************************************************************************
 * Private Data
 ****************************************************************************/

static atomic_t g_devno = ATOMIC_INITIALIZER(0);

/****************************************************************************
 * Private Functions
 ****************************************************************************/

/****************************************************************************
 * Name: unique_blkdev
 *
 * Description:
 *   Create a unique temporary device name in the /dev/ directory of the
 *   pseudo-file system.  We cannot use mktemp for this because it will
 *   attempt to open() the file.
 *
 * Input Parameters:
 *   None
 *
 * Returned Value:
 *   The allocated path to the device.  This must be released by the caller
 *   to prevent memory links.  NULL will be returned only the case where
 *   we fail to allocate memory.
 *
 ****************************************************************************/

static char *unique_blkdev (void)
{
    struct stat statbuf;
    char        devbuf[16];
    uint32_t    devno;
    int         ret;

    /* Loop until we get a unique device name */

    for (;;)
    {
        /* Get the next device number */

        devno = atomic_inc_return(&g_devno);

        /* Construct the full device number */

        devno &= 0xffffff;
        snprintf (devbuf, 16, "/dev/tmpb%06lx", (unsigned long)devno);

        /* Make sure that file name is not in use */

        ret = vfs_stat (devbuf, &statbuf, 1);
        if (ret < 0)
        {
            assert (ret == -ENOENT);
            return strdup (devbuf);
        }

        /* It is in use, try again */
    }
}

/****************************************************************************
 * Public Functions
 ****************************************************************************/

/* Helper: wrap an MTD path with FTL/Dhara and return the temp block device path.
 * On success, returns 0 and fills out_path with the created device path.
 * On failure, returns negative errno. */
int mtdproxy_prepare_blockdev_path(const char *mtddev, int mountflags, char *out_path, size_t out_sz)
{
    struct inode *mtd;
    char         *blkdev;
    int           ret;

    if (!mtddev || !out_path || out_sz < 16)
        return -EINVAL;

    blkdev = unique_blkdev ();
    if (blkdev == NULL)
    {
        KLOG_E ("ERROR: Failed to create temporary device name");
        return -ENOMEM;
    }

    ret = find_mtddriver (mtddev, &mtd);
    if (ret < 0)
    {
        KLOG_E ("ERROR: Failed to find %s mtd driver", mtddev);
        free(blkdev);
        return ret;
    }

#ifdef CONFIG_MTD_DHARA
    if (strcmp(mtddev, "/dev/mtdrootfs") == 0) {
        KLOG_I("Using Dhara FTL for %s -> %s", mtddev, blkdev);
        ret = dhara_initialize_by_path(blkdev, mtd->u.i_mtd);
    } else {
        KLOG_I("Using raw FTL shim for %s -> %s (readonly path)", mtddev, blkdev);
        ret = ftl_initialize_by_path(blkdev, mtd->u.i_mtd);
    }
#else
    ret = ftl_initialize_by_path (blkdev, mtd->u.i_mtd);
#endif
    inode_release (mtd);
    if (ret < 0)
    {
        KLOG_E ("ERROR: FTL init by path (%s <- %s) failed: %d", blkdev, mtddev, ret);
        free (blkdev);
        return ret;
    }

    /* 成功：把路径拷贝给调用者 */
    strlcpy(out_path, blkdev, out_sz);
    /* 不在此处 unlink；让上层在 mount 成功后打开并 unlink，或在失败时清理 */
    free(blkdev);
    return 0;
}

/****************************************************************************
 * Name: mtd_proxy
 *
 * Backward-compatible wrapper that opens the block driver and returns inode*.
 ****************************************************************************/
int mtd_proxy (const char *mtddev, int mountflags, struct inode **ppinode)
{
    char blkbuf[32];
    int ret = mtdproxy_prepare_blockdev_path(mtddev, mountflags, blkbuf, sizeof(blkbuf));
    KLOG_I("mtd_proxy: prepare %s -> %s ret=%d", mtddev, blkbuf, ret);
    if (ret < 0)
        return ret;

    ret = open_blockdriver (blkbuf, mountflags, ppinode);
    KLOG_I("mtd_proxy: open_blockdriver(%s) ret=%d", blkbuf, ret);
    if (ret < 0)
    {
        KLOG_E ("ERROR: Failed to open %s: %d", blkbuf, ret);
        vfs_unlink (blkbuf);
        return ret;
    }

    /* 由调用者在适当时机 unlink。此处不 unlink，避免多次创建/打开时竞态 */
    return 0;
}
#endif