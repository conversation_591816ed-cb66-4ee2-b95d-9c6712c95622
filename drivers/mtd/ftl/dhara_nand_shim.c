#include "dhara/dhara/nand.h"
#include <stddef.h>
#include <stdint.h>

/* Forward declarations of glue functions */
int dhara_glue_read(const struct dhara_nand *n, dhara_page_t p, size_t off, size_t len,
                    uint8_t *data, dhara_error_t *err);
int dhara_glue_prog(const struct dhara_nand *n, dhara_page_t p, const uint8_t *data,
                    dhara_error_t *err);
int dhara_glue_erase(const struct dhara_nand *n, dhara_block_t b, dhara_error_t *err);
int dhara_glue_is_free(const struct dhara_nand *n, dhara_page_t p);
int dhara_glue_is_bad(const struct dhara_nand *n, dhara_block_t b);

/* Define the extern symbols Dhara calls */
int dhara_nand_read(const struct dhara_nand *n, dhara_page_t p, size_t off, size_t len,
                    uint8_t *data, dhara_error_t *err)
{
    return dhara_glue_read(n, p, off, len, data, err);
}

int dhara_nand_prog(const struct dhara_nand *n, dhara_page_t p, const uint8_t *data,
                    dhara_error_t *err)
{
    return dhara_glue_prog(n, p, data, err);
}

int dhara_nand_erase(const struct dhara_nand *n, dhara_block_t b, dhara_error_t *err)
{
    return dhara_glue_erase(n, b, err);
}

int dhara_nand_is_free(const struct dhara_nand *n, dhara_page_t p)
{
    return dhara_glue_is_free(n, p);
}

int dhara_nand_is_bad(const struct dhara_nand *n, dhara_block_t b)
{
    return dhara_glue_is_bad(n, b);
}

int dhara_nand_copy(const struct dhara_nand *n, dhara_page_t src, dhara_page_t dst,
                    dhara_error_t *err)
{
    (void)err;
    /* NOTE: For now, assume 2KiB page. If your NAND uses 4KiB pages, we'll detect later. */
    uint8_t tmp[2048];
    if (dhara_glue_read(n, src, 0, sizeof(tmp), tmp, NULL) < 0)
        return -1;
    if (dhara_glue_prog(n, dst, tmp, NULL) < 0)
        return -1;
    return 0;
}

void dhara_nand_mark_bad(const struct dhara_nand *n, dhara_block_t b)
{
    (void)n;
    (void)b;
    /* Deliberately no-op for first version to avoid accidental marking. */
}
