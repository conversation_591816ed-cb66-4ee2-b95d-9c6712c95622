#include <errno.h>
#include <lwip/dhcp.h>
#include <lwip/dns.h>
#include <lwip/tcpip.h>
#include <lwip/pbuf.h>
#include <net/ethernet_dev.h>
#include <net/if_arp.h>
#include <net/netdev.h>
#include <netif/bridgeif.h>
#include <netif/etharp.h>
#include <netinet/if_ether.h>
#include <system/kconfig.h>

#define KLOG_TAG "LWIP_ETH"
#include <klog.h>

#ifndef OK
#define OK 0
#endif

#ifndef ERROR
#define ERROR 1
#endif

#define NETIF_TO_DEV_NAME(lwip_netif) ((((ETH_DEV *)(lwip_netif->state))->netdev)->name)
#define NETPKT_RESERVED_TO_PBUF_CUSTOM(ptr) ((struct pbuf_custom *)(ptr))

unsigned int nebuf_alloc_err;
unsigned int pbuf_alloc_err;
unsigned int netconn_alloc_err;
unsigned int other_pool_err;

static int eth_lwip_broadcast_loopback(struct netif *netif, struct pbuf *pb);
static err_t eth_lwip_netif_add_callback(struct netif *lwip_netif);
extern int lwip_netdev_sync_flags(NET_DEV *netdev, void *netstack_data);
extern struct netif *netif_get_loopif(void);

err_t eth_lwip_status_callback(struct netif *lwip_netif)
{
    ETH_DEV *ethdev = (ETH_DEV *)lwip_netif->state;
    NET_DEV *netdev = ethdev->netdev;

    /* 对比状态变化，调用驱动up/down函数 */
    if ((netif_is_up(lwip_netif)) && (!netdev_is_up(netdev)))
    {
        if ((ethdev->drv_info != NULL) && (ethdev->drv_info->eth_func != NULL) &&
            (ethdev->drv_info->eth_func->ifup != NULL))
        {
            ethdev->drv_info->eth_func->ifup(ethdev);
        }
    }
    else if ((!netif_is_up(lwip_netif)) && (netdev_is_up(netdev)))
    {
        if (ethdev->drv_info->eth_func->ifdown != NULL)
        {
            ethdev->drv_info->eth_func->ifdown(ethdev);
        }
    }

    /* 与NET_DEV结构同步状态位 */
    return lwip_netdev_sync_flags(netdev, (void *)lwip_netif);
}

static err_t eth_lwip_link_callback(struct netif *lwip_netif)
{
    ETH_DEV *ethdev = (ETH_DEV *)lwip_netif->state;
    NET_DEV *netdev = ethdev->netdev;

    /* 对比状态变化，调用驱动up/down函数 */
    if ((netif_is_link_up(lwip_netif)) && (!netdev_is_link_up(netdev)))
    {
        // fixme 待实现
        // ethdev->phy_dev->link_up();
    }
    else if ((!netif_is_link_up(lwip_netif)) && (netdev_is_link_up(netdev)))
    {
        // fixme 待实现
        // ethdev->phy_dev->link_down();
    }

    return lwip_netdev_sync_flags(netdev, (void *)lwip_netif);
}

/* 以太网设备数据接收中间层 */
int eth_lwip_netif_input(ETH_DEV *ethdev, struct pbuf *pb)
{
    uint32_t *dest_addr = NULL;
    uint8_t *tmp_addr_1 = NULL;
    uint8_t *tmp_addr_2 = NULL;
    uint16_t *pptp_type = NULL;
    int i, j;
    long ip_flags;
    err_t ret = 0;

    ethdev->netdev->pkt_stats.in_bytes += (uint64_t)pb->tot_len;
    ethdev->netdev->pkt_stats.in_pacs += 1;

    if (ethdev->lwip_netif_count > 1)
    {
        /* 判断协议 */
        pptp_type = (uint16_t *)((char *)pb->payload + 12);

        switch ((uint32_t)(__ntohs(*pptp_type)))
        {
        case ETH_P_ARP:
            dest_addr = (uint32_t *)((char *)pb->payload + ETH_HDRLEN + 0x18);
            break;

        case ETH_P_IP:
            dest_addr = (uint32_t *)((char *)pb->payload + ETH_HDRLEN + 0x10);
            break;

        default:
            break;
        }

        spin_lock_irqsave(&ethdev->ip_lock, ip_flags);

        for (i = 0; i < ethdev->lwip_netif_count; i += 1)
        {
            if ((uintptr_t)dest_addr & 0x3)
            {
                tmp_addr_1 = (uint8_t *)dest_addr;
                tmp_addr_2 = (uint8_t *)&(ethdev->lwip_netif[i]->ip_addr.addr);

                for (j = 0; j < 4; j++)
                {
                    if (*tmp_addr_1 != *tmp_addr_2)
                    {
                        break;
                    }

                    tmp_addr_1++;
                    tmp_addr_2++;
                }

                if (j == 4)
                {
                    break;
                }
            }
            else
            {
                if (dest_addr && *dest_addr == ethdev->lwip_netif[i]->ip_addr.addr)
                {
                    break;
                }
            }
        }

        if (i == ethdev->lwip_netif_count)
        {
            spin_unlock_irqrestore(&ethdev->ip_lock, ip_flags);
            ethdev->lwip_netif[0]->input(pb, ethdev->lwip_netif[0]);
        }
        else
        {
            spin_unlock_irqrestore(&ethdev->ip_lock, ip_flags);
            if (ethdev->lwip_netif[i])
            {
                ethdev->lwip_netif[i]->input(pb, ethdev->lwip_netif[i]);
            }
            else
            {
                ret = -ENODEV;
            }
        }
    }
    else
    {
        ethdev->lwip_netif[0]->input(pb, ethdev->lwip_netif[0]);
    }

    if (ret < 0)
    {
        ethdev->netdev->pkt_stats.ein_pacs += 1;
    }

    return ret;
}

/*
    netpkt->buf指向pbuf结构体
*/
int eth_lwip_netpkt_to_desc(ETH_NETPKT *netpkt, void *desc_buf)
{
    unsigned int pb_offset;
    struct pbuf *temp_pb = NULL;
    unsigned char *buf = (unsigned char *)desc_buf;

    temp_pb = (struct pbuf *)netpkt->buf;
    pb_offset = 0;

    while (temp_pb)
    {
        memcpy(&(buf[pb_offset]), temp_pb->payload, temp_pb->len);
        pb_offset += temp_pb->len;
        temp_pb = temp_pb->next;
    }
}

/* 以太网设备数据发送中间层 */
err_t eth_lwip_netif_linkoutput(struct netif *netif, struct pbuf *pb)
{
    ETH_DEV *ethdev;
    ETH_NETPKT *netpkt = NULL;
    long send_flag;
    int ret;
#ifdef CONFIG_SUPPORT_PCAP_TOOL
    struct pbuf *tbuf = pb;
    struct pcap_nsock *pnsock;
    char buf[1600];
    uint32_t len = 0;
#endif

    ASSERT(netif != NULL);
    ethdev = (ETH_DEV *)netif->state;

    if (pb == NULL)
    {
        return ERR_ARG;
    }

#ifdef CONFIG_SUPPORT_PCAP_TOOL
    if (TTOS_OK == TTOS_ObtainMutex(PCAP_SOCKLIST_MUTEX, TTOS_MUTEX_WAIT_FOREVER))
    {
        list_for_each_entry(pnsock, &PCAP_NSOCK_LIST, node)
        {
            if (ethdev == pnsock->eth)
            {
                while (tbuf)
                {
                    memcpy(&buf[len], pb->payload, pb->len);
                    len += pb->len;
                    tbuf = tbuf->next;
                }
                
                TTOS_SendMsgq(pnsock->msgq, buf, pb->tot_len, 0, 0);
            }
        }
        TTOS_ReleaseMutex(PCAP_SOCKLIST_MUTEX);
    }
#endif

#if IS_ENABLED(CONFIG_LWIP_BROADCAST_LOOPBACK)
    eth_lwip_broadcast_loopback(netif, pb);
#endif

    netpkt = eth_netpkt_alloc(0);
    if (netpkt == NULL)
    {
        return ERR_MEM;
    }

    netpkt->buf = (unsigned char*)pb;
    netpkt->len = pb->tot_len;
    /* 标记netpkt的buf指向PBUF */
    netpkt->flags |= ETH_NETPKT_BUF_TO_PBUF;

    spin_lock_irqsave(&ethdev->send_lock, send_flag);
    ret = ethdev->drv_info->eth_func->send(ethdev, netpkt);
    spin_unlock_irqrestore(&ethdev->send_lock, send_flag);

    ethdev->netdev->pkt_stats.out_bytes += (uint64_t)pb->tot_len;
    ethdev->netdev->pkt_stats.out_pacs += 1;

    if (ret != OK)
    {
        if (ret == -ENONET)
        {
            ethdev->netdev->pkt_stats.dout_pacs += 1;
        }

        return ERR_IF;
    }

    return ERR_OK;
}

/* 添加IP地址 */
int eth_lwip_add_ip(ETH_DEV *ethdev, ETH_CFG_INFO *cfg_info)
{
    struct netif *lwip_netif = NULL;
    struct in_addr ipaddr, netmask, gw;
#if LWIP_NETIF_HOSTNAME
    char *hostname = NULL;
#endif
    long ip_flags;

    spin_lock_irqsave(&ethdev->ip_lock, ip_flags);
    if (ethdev == NULL || cfg_info == NULL || ethdev->lwip_netif[0] == NULL)
    {
        spin_unlock_irqrestore(&ethdev->ip_lock, ip_flags);
        return -EINVAL;
    }

    if (ethdev->lwip_netif_count == CONFIG_MAX_NETIF_IPS)
    {
        spin_unlock_irqrestore(&ethdev->ip_lock, ip_flags);
        return -ENOBUFS;
    }

    lwip_netif = (struct netif *)calloc(1, sizeof(struct netif) + LWIP_HOSTNAME_LEN);
    if (lwip_netif == NULL)
    {
        KLOG_E("malloc netif failed\n");
        spin_unlock_irqrestore(&ethdev->ip_lock, ip_flags);
        return -ENOMEM;
    }

    /* set netif */
    ethdev->lwip_netif[ethdev->lwip_netif_count] = lwip_netif;
    ethdev->lwip_netif_count += 1;
    lwip_netif->state = (void *)ethdev;

    /* set name */
    memcpy(lwip_netif->name, ethdev->drv_info->drv_name, sizeof(lwip_netif->name));

    /* set hw address to 6 */
    lwip_netif->hwaddr_len = NETIF_MAX_HWADDR_LEN;

    memcpy(lwip_netif->hwaddr, ethdev->lwip_netif[0]->hwaddr, lwip_netif->hwaddr_len);

    /* set linkoutput */
    lwip_netif->linkoutput = eth_lwip_netif_linkoutput;

#if LWIP_NETIF_HOSTNAME
    hostname = (char *)lwip_netif + sizeof(struct netif);
    sprintf(hostname, "intewell_%s%d", lwip_netif->name, ethdev->eth_unit);

    /* Initialize interface hostname */
    lwip_netif->hostname = hostname;
#endif /* LWIP_NETIF_HOSTNAME */

    ipaddr.s_addr = cfg_info->ip.s_addr;
    netmask.s_addr = cfg_info->netmask.s_addr;
    gw.s_addr = ethdev->lwip_netif[0]->gw.addr;

    if (NULL == netif_add(lwip_netif, (const ip4_addr_t *)&ipaddr, (const ip4_addr_t *)&netmask,
                          (const ip4_addr_t *)&gw, ethdev, eth_lwip_netif_add_callback,
                          tcpip_input))
    {
        KLOG_E("netif add FAILED!\n");
        free(lwip_netif);
        ethdev->lwip_netif_count -= 1;
        ethdev->lwip_netif[ethdev->lwip_netif_count] = NULL;
        spin_unlock_irqrestore(&ethdev->ip_lock, ip_flags);

        return -ERROR;
    }

    spin_unlock_irqrestore(&ethdev->ip_lock, ip_flags);

    if (netdev_is_link_up(ethdev->netdev))
    {
        netif_set_link_up(lwip_netif);
    }

    if (netdev_is_up(ethdev->netdev))
    {
        netif_set_up(lwip_netif);
    }

    return 0;
}

/* 删除IP地址，对于lwip为删除netif */
int eth_lwip_del_ip_by_netif(ETH_DEV *ethdev, struct netif *nif)
{
    bool f_del_ip = false;
    int i;
    long ip_flags;

    if (NULL == nif)
    {
        return -EINVAL;
    }

    if (ethdev->lwip_netif_count < 2)
    {
        return OK;
    }

    spin_lock_irqsave(&ethdev->ip_lock, ip_flags);

    for (i = 0; i < ethdev->lwip_netif_count; i++)
    {
        if (f_del_ip == false)
        {
            if (ethdev->lwip_netif[i] == nif)
            {
                f_del_ip = true;
            }
        }
        else
        {
            /* 删除时为移位，当前i为要移动到前一个的下标 */
            /* 当要移位的是第二个（下标等于1）时，代表要删除的是第一个，需额外处理 */
            if (i == 1)
            {
                /* 删除的为第一个时将第二个备份在第一个，最终释放第二个 */
                netif_set_addr(ethdev->lwip_netif[0], &ethdev->lwip_netif[1]->ip_addr,
                               &ethdev->lwip_netif[1]->netmask, &ethdev->lwip_netif[1]->gw);
                nif = ethdev->lwip_netif[1];
            }
            else
            {
                ethdev->lwip_netif[i - 1] = ethdev->lwip_netif[i];
            }
        }
    }

    if (f_del_ip == false)
    {
        spin_unlock_irqrestore(&ethdev->ip_lock, ip_flags);
        return -ESRCH;
    }
    else
    {
        nif->link_callback = NULL;
        nif->status_callback = NULL;
        netif_remove((struct netif *)nif);
        free(nif);
        ethdev->lwip_netif_count -= 1;
        ethdev->lwip_netif[ethdev->lwip_netif_count] = NULL;
        spin_unlock_irqrestore(&ethdev->ip_lock, ip_flags);
    }

    return ERR_OK;
}

/* 广播包本地套接字接收 */
static int eth_lwip_broadcast_loopback(struct netif *netif, struct pbuf *pb)
{
    ETH_DEV *ethdev;
    unsigned char pptp_type;
    unsigned short src_port;
    struct pbuf *pbuf_new, *pbuf_now, *pbuf_pre, *temp_pb;
    int offset;
    int ret;
    long ip_flags;

    ethdev = (ETH_DEV *)netif->state;

    /* 目的MAC必须为FF:FF:FF:FF:FF:FF */
    if (eth_is_broadcast_addr((const unsigned char *)pb->payload) == 0)
    {
        return (-1);
    }

    pptp_type = eth_ip_protocol_parse(pb->payload);
    if (pptp_type != IPPROTO_UDP)
    {
        return (-1);
    }

    /* 跳过DHCP协议 */
    src_port = eth_src_port_parse(pb->payload);
    if (src_port == 68)
    {
        return (-1);
    }

    temp_pb = pb;
    pbuf_new = NULL;
    pbuf_pre = NULL;

    do
    {
        if (temp_pb->type_internal == PBUF_TYPE_FLAG_STRUCT_DATA_CONTIGUOUS)
        {
            if ((uintptr_t)temp_pb->payload > (uintptr_t)temp_pb)
            {
                offset = (char *)temp_pb->payload - (char *)temp_pb - sizeof(struct pbuf);
            }
            else
            {
                offset = 0;
            }
        }
        else
        {
            offset = 0;
        }

        pbuf_now = pbuf_alloc(PBUF_RAW, temp_pb->len + offset, RX_PBUF_TYPE);
        pbuf_now->payload = (void *)((char *)pbuf_now->payload + offset);
        memcpy(pbuf_now->payload, temp_pb->payload, temp_pb->len);
        pbuf_now->next = NULL;
        pbuf_now->tot_len = temp_pb->tot_len;
        pbuf_now->len = temp_pb->len;
        pbuf_now->type_internal = temp_pb->type_internal;
        pbuf_now->flags = temp_pb->flags;
        pbuf_now->ref = 1;
        pbuf_now->if_idx = 0;

        if (pbuf_new == NULL)
        {
            pbuf_new = pbuf_now;
        }

        if (pbuf_pre != NULL)
        {
            pbuf_pre->next = pbuf_now;
        }

        pbuf_pre = pbuf_now;
        temp_pb = temp_pb->next;
    } while (temp_pb != NULL);

    ret = tcpip_input(pbuf_new, ethdev->lwip_netif[0]);

    return ret;
}

int eth_lwip_get_arp_entry(int i, char *ip, char *mac, unsigned char *state, char *dev_name)
{
    ip4_addr_t *ipaddr;
    struct netif *netif;
    struct eth_addr *eth_addr;
    unsigned char *etharp_entry_state;
    int ret;

    ret = etharp_get_entry(i, &ipaddr, &netif, &eth_addr);
    if (ret)
    {
        etharp_entry_state = (unsigned char *)((char *)(eth_addr + 1) + 2);

        /* ETHARP_STATE_STATIC */
        if (*etharp_entry_state == 0x5)
        {
            *state = (ATF_COM | ATF_PERM);
        }
        else
        {
            *state = *etharp_entry_state;
        }

        sprintf(ip, "%d.%d.%d.%d", ip4_addr1(ipaddr), ip4_addr2(ipaddr), ip4_addr3(ipaddr),
                ip4_addr4(ipaddr));
        sprintf(mac, "%x:%x:%x:%x:%x:%x", eth_addr->addr[0], eth_addr->addr[1], eth_addr->addr[2],
                eth_addr->addr[3], eth_addr->addr[4], eth_addr->addr[5]);
        sprintf(dev_name, "%s", NETIF_TO_DEV_NAME(netif));
    }

    return ret;
}

// TODO: Bridge Control
struct netif *eth_lwip_bridge_create()
{
    ip4_addr_t br_ip, br_netmask, br_gw;
    struct netif *bridge_netif = NULL;

    bridgeif_initdata_t bridge_initdata =
        BRIDGEIF_INITDATA1(2, 1024, 16, ETH_ADDR(0, 1, 2, 3, 4, 5));

    bridge_netif = (struct netif *)calloc(1, sizeof(struct netif));

    br_ip.addr = inet_addr("********");
    br_netmask.addr = inet_addr("*************");
    br_gw.addr = inet_addr("********");

    netif_add(bridge_netif, (const ip4_addr_t *)&br_ip, (const ip4_addr_t *)&br_netmask,
              (const ip4_addr_t *)&br_gw, &bridge_initdata, bridgeif_init, tcpip_input);

    netif_set_link_up(bridge_netif);
    netif_set_up(bridge_netif);

    return bridge_netif;
}

ETH_NETPKT *eth_lwip_alloc_pool_pbuf(unsigned int len)
{
    ETH_NETPKT *netpkt = NULL;
    struct pbuf *pbuf = NULL;

    /*
        pbuf                  netpkt                      ┌─────┐                                     pbuf->payload
        ↓                     ↓                           │     ↓                                     ↓
        ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┐
        │     │        │      │┌──────────────────────────────ETH_NETPKT─────────────────────────────┐                          │
        │*next│*payload│······│[*base next refs flags len *buf reserved_len *reserved][reserved space][······Real Payload······]│
        │     │        │      │└────────────────────────────────────────────payload────────────────────────────────────────────┘│
        └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┘
        ↑                          │                   │    │                                         ↑                        │
        └──────────────────────────┘                   │    └─────────────────────────────────────────┘                        │
                                                       └─────────────────────────────────────────────→└───────────len──────────┘
        pbuf payload len = sizeof(ETH_NETPKT) + CONFIG_NETPKT_RESERVED_LEN + len
    */
    pbuf = pbuf_alloc(PBUF_RAW, len + sizeof(ETH_NETPKT) + CONFIG_NETPKT_RESERVED_LEN, PBUF_POOL);
    if (pbuf == NULL)
    {
        return NULL;
    }

    netpkt = (ETH_NETPKT *)pbuf->payload;
    
    netpkt->base = pbuf;
    netpkt->next = NULL;
    /* 此类型netpkt的引用计数无效 */
    netpkt->refs.counter = 1;
    netpkt->flags = ETH_NETPKT_WITHIN_PBUF;
    netpkt->len = len;
    netpkt->buf = (unsigned char *)pbuf->payload + sizeof(ETH_NETPKT) + CONFIG_NETPKT_RESERVED_LEN;
    netpkt->reserved = (unsigned char *)pbuf->payload + sizeof(ETH_NETPKT);
    pbuf->payload = (void *)(netpkt->buf);

    return netpkt;
}

void eth_lwip_pbuf_costom_free(struct pbuf *p)
{
    ETH_NETPKT *netpkt = NULL;
    //unsigned char **q = (unsigned char **)((unsigned char *)p - sizeof(unsigned char *));

    //netpkt = container_of(q, ETH_NETPKT, reserved);
    netpkt = (ETH_NETPKT *)((unsigned char *)p - sizeof(ETH_NETPKT));

    eth_netpkt_free(netpkt);
}

struct pbuf *eth_lwip_alloc_custom_pbuf(ETH_NETPKT *netpkt)
{
    struct pbuf *pbuf = NULL;

    if (netpkt->reserved_len < sizeof(struct pbuf_custom))
    {
        return NULL;
    }

    /*
          netpkt
            │                             ┌───────────────────────────────────────────────────┐
            │                             │                   ┌────┐                          │     
            ↓                             │                   │    ↓                          ↓ 
            ┌────────────────────────────────────────────────────────────────────────────────────────────────────┐
            │     │     │    │     |   │    │            │         │┌─────reserved space─────┐│                  │
            │*base│*next│refs│flags|len│*buf│reserved_len|*reserved│[struct pbuf_custom······]│[······Data······]│
            │     │     │    │     |   │    │            │         │ pbuf custom_free_function│                  │
            └────────────────────────────────────────────────────────────────────────────────────────────────────┘
            ↑  │                                                       │                      ↑
            └──┘                                                       └─────pbuf->payload────┘
    */

    pbuf = pbuf_alloced_custom(PBUF_RAW, netpkt->len, PBUF_REF, NETPKT_RESERVED_TO_PBUF_CUSTOM(netpkt->reserved),
                               (void *)netpkt->buf, (uint16_t)(netpkt->len));
    if (pbuf == NULL)
    {
        return NULL;
    }

    NETPKT_RESERVED_TO_PBUF_CUSTOM(netpkt->reserved)->custom_free_function = (pbuf_free_custom_fn)eth_lwip_pbuf_costom_free;
    
    return pbuf;
}

static err_t eth_lwip_netif_add_callback(struct netif *lwip_netif)
{
    ETH_DEV *ethdev;

    ethdev = (ETH_DEV *)lwip_netif->state;
    if (ethdev == NULL)
    {
        return ERR_IF;
    }

    /* maximum transfer unit */
    lwip_netif->mtu = ETHERNET_MTU;
    KLOG_D("mtu %u", (u32_t)lwip_netif->mtu);

    /* set output */
    lwip_netif->output = etharp_output;

    if (ethdev->lwip_netif_count == 1)
    {
        /* 仅为ethernetif中的第一个lwip_netif安装状态变化回调函数，用于与NET_DEV同步设备标志位 */
        netif_set_status_callback(lwip_netif, (netif_status_callback_fn)eth_lwip_status_callback);
        netif_set_link_callback(lwip_netif, (netif_status_callback_fn)eth_lwip_link_callback);
    }

    lwip_netif->flags =
        NETIF_FLAG_BROADCAST | NETIF_FLAG_IGMP | NETIF_FLAG_ETHARP | NETIF_FLAG_ETHERNET;

#if LWIP_IPV6
    lwip_netif->output_ip6 = ethip6_output;
    lwip_netif->ip6_autoconfig_enabled = 1;
    netif_create_ip6_linklocal_address(lwip_netif, 1);
#if LWIP_IPV6_MLD
    lwip_netif->flags |= NETIF_FLAG_MLD6;

    /*
     * For hardware/netifs that implement MAC filtering.
     * All-nodes link-local is handled by default, so we must let the hardware
     * know to allow multicast packets in. Should set mld_mac_filter previously.
     */
    if (lwip_netif->mld_mac_filter != NULL)
    {
        ip6_addr_t ip6_allnodes_ll;
        ip6_addr_set_allnodes_linklocal(&ip6_allnodes_ll);
        lwip_netif->mld_mac_filter(lwip_netif, &ip6_allnodes_ll, NETIF_ADD_MAC_FILTER);
    }
#endif /* LWIP_IPV6_MLD */
#endif /* LWIP_IPV6 */

    return OK;
}

int eth_lwip_netdev_install_callback(NET_DEV *netdev, struct netif *lwip_netif)
{
    lwip_netdev_sync_flags(netdev, (void *)lwip_netif);

    return OK;
}

void eth_lwip_dns_update_hook()
{
    char dns_buf[128];
    int dns_count = 0;
    ip_addr_t *dns_addr;
    int offset = 0;
    int i;
    int ret;

    memset(dns_buf, 0, sizeof(dns_buf));

    for (i = 0; i < DNS_MAX_SERVERS; i++)
    {
        dns_addr = (ip_addr_t *)dns_getserver((unsigned char)i);
        if (strcmp(ip_ntoa(dns_addr), "0.0.0.0") != 0)
        {
            sprintf(dns_buf + offset, "%s;", ip_ntoa(dns_addr));
            offset = strlen(dns_buf);

            dns_count++;
        }
    }

    eth_update_resolv_file(dns_count, (const char *)dns_buf);
}

void eth_lwip_data_to_stack(ETH_DEV *ethdev, ETH_NETPKT *netpkt)
{
    struct pbuf *pb = NULL;
    
#ifdef CONFIG_LWIP_RX_USE_PBUF_POOL
    pb = (struct pbuf *)netpkt->base;
    pb->payload = (void *)netpkt->buf;
    pb->len = netpkt->len;
    pb->tot_len = pb->len;
    eth_lwip_netif_input(ethdev, pb);
#else
    pb = eth_lwip_alloc_custom_pbuf(netpkt);

    if (pb != NULL)
    {
        eth_lwip_netif_input(ethdev, pb);
    }
    else
    {
        eth_netpkt_free(netpkt);
    }
#endif
}

void *eth_lwip_device_init(ETH_DEV *ethdev, const char *name, const uint8_t *const mac_addr)
{
    struct netif *lwip_netif = NULL;
#if LWIP_NETIF_HOSTNAME
    char *hostname = NULL;
#endif
    ip4_addr_t default_netmask;

    lwip_netif = (struct netif *)calloc(1, sizeof(struct netif) + LWIP_HOSTNAME_LEN);
    if (lwip_netif == NULL)
    {
        KLOG_E("malloc netif failed\n");
        goto err_eth_dev_init;
    }

    /* set netif */
    ethdev->lwip_netif[0] = lwip_netif;
    ethdev->lwip_netif_count = 1;
    lwip_netif->state = (void *)ethdev;

    /* set name */
    memcpy(lwip_netif->name, name, sizeof(lwip_netif->name));

    /* set hw address to 6 */
    lwip_netif->hwaddr_len = NETIF_MAX_HWADDR_LEN;

    memcpy(lwip_netif->hwaddr, mac_addr, lwip_netif->hwaddr_len);

    /* set linkoutput */
    lwip_netif->linkoutput = eth_lwip_netif_linkoutput;

    default_netmask.addr = inet_addr("*************");

    if (NULL == netif_add(lwip_netif, NULL, (const ip4_addr_t *)&default_netmask, NULL, ethdev,
                          eth_lwip_netif_add_callback, tcpip_input))
    {
        KLOG_E("netif add FAILED!\n");

        goto err_eth_dev_init;
    }

#if LWIP_NETIF_HOSTNAME
    hostname = (char *)lwip_netif + sizeof(struct netif);
    sprintf(hostname, "intewell_%s%d", lwip_netif->name, ethdev->eth_unit);

    /* Initialize interface hostname */
    lwip_netif->hostname = hostname;
#endif /* LWIP_NETIF_HOSTNAME */

    return lwip_netif;

err_eth_dev_init:

    if (lwip_netif != NULL)
    {
        free(lwip_netif);
    }

    return NULL;
}

/*
    在LwIP的netif_loop_output()函数中调用，用于统计环回网卡数据发送信息
    flag为0表示正常发送的数据包
    flag为1表示异常丢弃的数据包
*/
void eth_lwip_loopback_output_hook(struct netif *netif, int flag, unsigned int len)
{
    ETH_DEV *ethdev = (ETH_DEV *)netif->state;

    switch (flag)
    {
    case 0:
        ethdev->netdev->pkt_stats.out_bytes += (uint64_t)len;
        ethdev->netdev->pkt_stats.out_pacs += 1;
        break;
    case 1:
        ethdev->netdev->pkt_stats.eout_pacs += 1;
        ethdev->netdev->pkt_stats.dout_pacs += 1;
        break;
    default:
        break;
    }

    return;
}

/* 配置并向系统注册LwIP的环回网卡 */
void *eth_lwip_loopback_init(ETH_DEV *ethdev_lo)
{
    struct netif *netif_lo = NULL;
    int i;

    netif_lo = netif_get_loopif();
    if (strncmp(netif_lo->name, "lo", 2) != 0)
    {
        return (NULL);
    }

    ethdev_lo->lwip_netif[0] = netif_lo;
    ethdev_lo->lwip_netif_count = 1;
    netif_lo->state = (void *)ethdev_lo;

    netif_lo->hwaddr_len = NETIF_MAX_HWADDR_LEN;

    for (i = 0; i < netif_lo->hwaddr_len; i++)
    {
        netif_lo->hwaddr[i] = 0x0;
    }

    netif_lo->mtu = 65535;

    netif_set_status_callback(netif_lo, (netif_status_callback_fn)eth_lwip_status_callback);

    return (netif_lo);
}

/*
    LwIP钩子函数LWIP_HOOK_IP4_INPUT
    目前仅用于统计LoopBack网卡接收数据统计
*/
int eth_lwip_ip4_input_hook(struct pbuf *pbuf, struct netif *input_netif)
{
    ETH_DEV *ethdev = NULL;

    if (strcmp(input_netif->name, "lo") != 0)
    {
        return 0;
    }

    ethdev = (ETH_DEV *)input_netif->state;

    ethdev->netdev->pkt_stats.in_bytes += (uint64_t)pbuf->tot_len;
    ethdev->netdev->pkt_stats.in_pacs += 1;

    /* 注意必须返回0，否则LwIP不会继续完成包的处理 */
    return 0;
}

void eth_lwip_del_dev(struct netif *lwip_netif)
{
    ASSERT(lwip_netif);

#if LWIP_DHCP
    dhcp_stop(lwip_netif);
    dhcp_cleanup(lwip_netif);
#endif
    netif_set_down(lwip_netif);
    netif_remove(lwip_netif);

    free(lwip_netif);
}

int eth_lwip_memp_error_hook(memp_t type)
{
    switch (type)
    {
        case MEMP_NETBUF:
            nebuf_alloc_err++;
            break;
        case MEMP_PBUF:
            pbuf_alloc_err++;
            break;
        case MEMP_NETCONN:
            netconn_alloc_err++;
            break;
        default:
            other_pool_err++;
    }
}
