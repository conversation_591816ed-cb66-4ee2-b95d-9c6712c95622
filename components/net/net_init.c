#include <arch/sys_arch.h>
#include <net/ethernet_dev.h>
#include <net/netdev.h>
#include <ttos_init.h>

#define KLOG_TAG "NETWORK_INIT"
#include <klog.h>

extern int ttos_lwip_init();
extern void phy_subsystem_init();
extern void eth_pcap_register_sockif();

static int ttos_net_init()
{
#if IS_ENABLED(CONFIG_LWIP_2_2_0)
    ttos_lwip_init();
#endif

#ifdef CONFIG_SUPPORT_PCAP_TOOL
    eth_pcap_register_sockif();
#endif

    phy_subsystem_init();

    return 0;
}

INIT_EXPORT_COMPONENTS(ttos_net_init, "Init Network and Protocol Stack");
