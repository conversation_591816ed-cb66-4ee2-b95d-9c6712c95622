#include <driver/devbus.h>
#include <driver/device.h>
#include <driver/driver.h>
#include <driver/of.h>
#include <errno.h>
#include <stddef.h>
#include <stdlib.h>
#include <string.h>
#include <ttos_init.h>

#include "fmsh_nfc.h" /* Use register/command macros only */
#include <fs/fs.h>
#include <mtd/mtd.h>
#include <string.h>

#define KLOG_TAG "fmsh-nand-mtd"
#include <klog.h>

#ifndef container_of
#define container_of(ptr, type, member) ((type *)((char *)(ptr)-offsetof(type, member)))
#endif

/* Read-only, safe MTD over FMSH NFC (PIO first). Write/Erase remain -ENOSYS
 * to avoid any chance of corrupting bootloader areas. */

struct fmsh_nand_priv
{
    void *regs;  /* ioremap'd base */
    int use_dma; /* 0: PIO (forced), 1: DMA */

    /* Geometry and public MTD */
    struct mtd_dev_s mtd;           /* exported MTD */
    struct mtd_geometry_s geometry; /* cached geometry */
    uint32_t oobsize;               /* OOB size per page in bytes (e.g., 64) */

    /* ECC configuration */
    int ecc_enabled;  /* 0: off, 1: hw */
    int ecc_strength; /* bits per step, e.g., 8 */
    int ecc_step;     /* bytes per step, e.g., 512 */

    /* BBM bitmap and error counters */
    uint8_t *bbm_bitmap;       /* bit per erase block: 1=bad */
    uint16_t *uncorr_cnt;      /* uncorrectable read error counters per erase block */
    uint32_t uncorr_threshold; /* default 3 (configurable) */

    /* Derived geometry */
    uint32_t pages_per_erase; /* erase size / page size */

    /* Rootfs erase-block range (inclusive start, exclusive end) for marking policy */
    uint32_t rootfs_efirst;
    uint32_t rootfs_eend;
};

static inline uint32_t nfc_readl(struct fmsh_nand_priv *p, uint32_t off)
{
    return *(volatile uint32_t *)((uintptr_t)p->regs + off);
}
static inline void nfc_writel(struct fmsh_nand_priv *p, uint32_t val, uint32_t off)
{
    *(volatile uint32_t *)((uintptr_t)p->regs + off) = val;
}
static inline void nfc_writeb(struct fmsh_nand_priv *p, uint8_t val, uint32_t off)
{
    *(volatile uint8_t *)((uintptr_t)p->regs + off) = val;
}

static inline void nfc_fifo_flush(struct fmsh_nand_priv *p)
{
    nfc_writel(p, FIFO_INIT_FIFO_INIT, FIFO_INIT_REG);
}

static inline void nfc_clear_ints(struct fmsh_nand_priv *p)
{
    /* Reference driver clears by writing 0 */
    nfc_writel(p, 0, INT_STATUS_REG);
    (void)nfc_readl(p, INT_STATUS_REG);
}

/* Busy-wait for a condition with simple timeout */
static int nfc_wait_bits(struct fmsh_nand_priv *p, uint32_t reg, uint32_t mask, uint32_t expect_set,
                         uint32_t loops)
{
    while (loops--)
    {
        uint32_t v = nfc_readl(p, reg);
        if (expect_set)
        {
            if ((v & mask) == mask)
                return 0;
        }
        else
        {
            if ((v & mask) == 0)
                return 0;
        }
    }
    // uint32_t v = nfc_readl(p, reg);
    // KLOG_I("wait_bits timeout: reg=0x%02x mask=0x%08x expect=%u last=0x%08x", reg, mask,
    // expect_set,
    //        v);
    return -ETIMEDOUT;
}

/* Issue a command value to COMMAND_REG and optionally wait for CMD_END */
static int nfc_issue_command(struct fmsh_nand_priv *p, uint32_t cmd)
{
    nfc_clear_ints(p);
    nfc_writel(p, cmd, COMMAND_REG);
    /* Wait for command end */
    return nfc_wait_bits(p, INT_STATUS_REG, INT_STATUS_CMD_END_INT_FL, 1, 1000000);
}

/* Minimal PIO page read into dst. len must be <= page size. */
static int nfc_read_page_pio(struct fmsh_nand_priv *p, uint32_t page, uint32_t column, uint8_t *dst,
                             uint32_t len)
{
    /* Program address */
    nfc_writel(p, column, ADDR0_COL_REG);
    nfc_writel(p, page, ADDR0_ROW_REG);

    /* Choose 5-cycle read sequence for large chips (>=128MiB). Our chip is 512MiB */
    nfc_writel(p, GEN_SEQ_CTRL_READ_PAGE_5CYCLE, GEN_SEQ_CTRL_REG);

    /* Setup data size and flush FIFO */
    nfc_writel(p, len, DATA_SIZE_REG);
    nfc_fifo_flush(p);

    /* Use SIU FIFO read command */
    int ret = nfc_issue_command(p, COMMAND_READ_PAGE_STD);
    if (ret < 0)
        return ret;

    /* Read from FIFO_DATA_REG; poll DF_R_EMPTY to know availability */
    uint32_t remaining = len;
    uint8_t *ptr = dst;
    while (remaining)
    {
        /* Wait until data FIFO is not empty (DF_R_EMPTY == 0) */
        ret = nfc_wait_bits(p, FIFO_STATE_REG, FIFO_STATE_DF_R_EMPTY, 0, 1000000);
        if (ret < 0)
            return ret;
        uint32_t val = nfc_readl(p, FIFO_DATA_REG);
        uint32_t chunk = remaining >= 4 ? 4 : remaining;
        memcpy(ptr, &val, chunk);
        ptr += chunk;
        remaining -= chunk;
    }
    return 0;
}

static int fmsh_mtd_erase(struct mtd_dev_s *dev, off_t startblock, size_t nblocks)
{
    struct fmsh_nand_priv *priv = container_of(dev, struct fmsh_nand_priv, mtd);
    /* Erase in units of erase blocks. startblock is erase-block index. */
    uint32_t pages_per_erase = priv->geometry.erasesize / priv->geometry.blocksize;

    // KLOG_I("erase startblock=%llu nblocks=%zu", (unsigned long long)startblock, nblocks);

    for (size_t i = 0; i < nblocks; i++)
    {
        uint32_t erase_index = (uint32_t)(startblock + i);
        uint32_t page = erase_index * pages_per_erase; /* first page of this erase block */
        /* Set row address to any page within the block (use first) */
        nfc_writel(priv, 0, ADDR0_COL_REG);
        nfc_writel(priv, page, ADDR0_ROW_REG);
        /* Use proper general sequence for erase (3-cycle row address) */
        int ret = nfc_issue_command(priv, COMMAND_BLOCK_ERASE);
        if (ret < 0)
            return ret;
        /* Some controllers signal both CMD_END and MEM_RDY; wait for ready to be safe */
        ret = nfc_wait_bits(priv, INT_STATUS_REG, INT_STATUS_MEM0_RDY_INT_FL, 1, 1000000);
        if (ret < 0)
            return ret;
        /* Quick verify: read first page and ensure all 0xFF */
        uint32_t pagesz = priv->geometry.blocksize;
        uint8_t tmp[4096];
        if (pagesz > sizeof(tmp))
            return -EINVAL;
        ret = nfc_read_page_pio(priv, page, 0, tmp, pagesz);
        if (ret < 0)
            return ret;
        for (uint32_t t = 0; t < pagesz; t++)
        {
            if (tmp[t] != 0xFF)
            {
                (void)priv->mtd.markbad(&priv->mtd, erase_index);
                KLOG_W("erase-verify failed: eb=%u", erase_index);
                return -EIO;
            }
        }
    }
    return 0;
}

static int fmsh_markbad_if_needed(struct fmsh_nand_priv *priv, uint32_t page)
{
    /* 根据 ECC 状态决定是否累加并触发 markbad（仅 rootfs 范围） */
    uint32_t eb = page / priv->pages_per_erase;
    uint32_t eccs = nfc_readl(priv, ECC_STAT_REG);
    if (eccs & ECC_STAT_UNC_0)
    {
        if (priv->uncorr_cnt && ++priv->uncorr_cnt[eb] >= priv->uncorr_threshold)
        {
            /* 仅 rootfs 范围内允许，复用 mtd->markbad 回调 */
            return priv->mtd.markbad(&priv->mtd, eb);
        }
    }
    return 0;
}

static ssize_t fmsh_mtd_bread(struct mtd_dev_s *dev, off_t startblock, size_t nblocks,
                              uint8_t *buffer)
{
    struct fmsh_nand_priv *priv = container_of(dev, struct fmsh_nand_priv, mtd);
    const uint32_t page = priv->geometry.blocksize;
    uint8_t *dst = buffer;

    /* Force PIO */
    priv->use_dma = 0;

    for (size_t i = 0; i < nblocks; i++)
    {
        uint32_t pg = (uint32_t)(startblock + i);
        int ret = nfc_read_page_pio(priv, pg, 0, dst, page);
        if (ret < 0)
            return ret;
        (void)fmsh_markbad_if_needed(priv, pg);
        dst += page;
    }
    return (ssize_t)nblocks;
}

/* Program one page via PIO (full page write) */
static int nfc_prog_page_pio(struct fmsh_nand_priv *p, uint32_t page, const uint8_t *src,
                             uint32_t len)
{
    /* Only support full main-area page writes for now */
    uint32_t pagesz = p->geometry.blocksize;
    if (len != pagesz)
        return -EINVAL;

    // KLOG_HEX_I("", DUMP_PREFIX_OFFSET, 16, 1, src, len, true);

    /* Addresses */
    // KLOG_I("prog page=%u len=%u", page, len);
    nfc_writel(p, 0, ADDR0_COL_REG);
    /* Wait device ready before starting program */
    if (nfc_wait_bits(p, STATUS_REG, STATUS_MEM0_ST, 1, 100000) < 0)
    {
        uint32_t st = nfc_readl(p, STATUS_REG);
        KLOG_W("not ready before program, STATUS=0x%08x", st);
        return -ETIMEDOUT;
    }

    nfc_writel(p, page, ADDR0_ROW_REG);
    uint32_t cmd = GEN_SEQ_CTRL_WRITE_PAGE_5CYCLE;
    nfc_writel(p, cmd, GEN_SEQ_CTRL_REG);
    // KLOG_I("gen_seq_ctrl=0x%08x", cmd);
    /* Program addresses before issuing command */

    /* Sequence for write page (5-cycle) */
    nfc_writel(p, GEN_SEQ_CTRL_WRITE_PAGE_5CYCLE, GEN_SEQ_CTRL_REG);

    /* Set transfer size and flush FIFO */
    uint32_t xfer = (len + 3) & ~3u;
    nfc_writel(p, xfer, DATA_SIZE_REG);
    nfc_fifo_flush(p);
    uint32_t fstate = nfc_readl(p, FIFO_STATE_REG);
    // KLOG_I("fifo fill start len=%u fstate=0x%08x", len, fstate);

    /* Issue write command first to enable FIFO consumption, then stream data */
    nfc_clear_ints(p);
    nfc_writel(p, COMMAND_WRITE_PAGE_FIFO_GEN, COMMAND_REG);
    // KLOG_I("issued write cmd, start streaming");

    /* Stream the entire page into FIFO (32-bit words) */
    const uint8_t *ptr = src;
    uint32_t remaining = len;
    while (remaining)
    {
        int retw = nfc_wait_bits(p, FIFO_STATE_REG, FIFO_STATE_DF_W_FULL, 0, 1000000);
        /* Wait until DF_W_FULL==0 before each word write */

        if (retw < 0)
            return retw;
        uint32_t val = 0;
        uint32_t chunk = remaining >= 4 ? 4u : remaining;
        memcpy(&val, ptr, chunk);
        nfc_writel(p, val, FIFO_DATA_REG);
        ptr += chunk;
        remaining -= chunk;
    }

    /* Wait for command end and ready */
    {
        int ret = nfc_wait_bits(p, INT_STATUS_REG, INT_STATUS_CMD_END_INT_FL, 1, 1000000);
        if (ret < 0)
            return ret;
        ret = nfc_wait_bits(p, INT_STATUS_REG, INT_STATUS_MEM0_RDY_INT_FL, 1, 1000000);
        if (ret < 0)
            return ret;
    }
    return 0;
}

static ssize_t fmsh_mtd_bwrite(struct mtd_dev_s *dev, off_t startblock, size_t nblocks,
                               const uint8_t *buffer)
{
    struct fmsh_nand_priv *priv = container_of(dev, struct fmsh_nand_priv, mtd);

    /* Force PIO */
    priv->use_dma = 0;

    // KLOG_I("bwrite start block=%ld n=%u", (long)startblock, (unsigned)nblocks);

    uint32_t pagesz = priv->geometry.blocksize;
    const uint8_t *src = buffer;

    for (size_t i = 0; i < nblocks; i++)
    {
        uint32_t page_index = (uint32_t)(startblock + i);
        int ret = nfc_prog_page_pio(priv, page_index, src, pagesz);
        if (ret < 0)
        {
            uint32_t eb = page_index / priv->pages_per_erase;
            (void)priv->mtd.markbad(&priv->mtd, eb);
            return ret;
        }
        /* 写后回读校验 */
        uint8_t tmp[4096];
        if (pagesz > sizeof(tmp))
            return -EINVAL;
        ret = nfc_read_page_pio(priv, page_index, 0, tmp, pagesz);
        if (ret < 0)
        {
            uint32_t eb = page_index / priv->pages_per_erase;
            (void)priv->mtd.markbad(&priv->mtd, eb);
            return ret;
        }
        if (memcmp(tmp, src, pagesz) != 0)
        {
            uint32_t eb = page_index / priv->pages_per_erase;
            (void)priv->mtd.markbad(&priv->mtd, eb);
            KLOG_W("write-verify failed: eb=%u pg=%u", eb, page_index);
            return -EIO;
        }
        src += pagesz;
    }
    return (ssize_t)nblocks;
}

static ssize_t fmsh_mtd_read(struct mtd_dev_s *dev, off_t offset, size_t nbytes, uint8_t *buffer)
{
    struct fmsh_nand_priv *priv = container_of(dev, struct fmsh_nand_priv, mtd);
    const uint32_t page = priv->geometry.blocksize;

    /* Align down to page, then read page-by-page */
    off_t page_index = offset / page;
    off_t in_page_off = offset % page;

    uint8_t *dst = buffer;
    size_t remaining = nbytes;

    /* If not page-aligned, read first page and copy tail */
    if (in_page_off)
    {
        uint8_t tmp[4096];
        if (page > sizeof(tmp))
            return -EINVAL; /* unexpected page size */
        int ret = nfc_read_page_pio(priv, (uint32_t)page_index, 0, tmp, page);
        if (ret < 0)
            return ret;
        size_t chunk =
            (size_t)((remaining < (page - in_page_off)) ? remaining : (page - in_page_off));
        memcpy(dst, tmp + in_page_off, chunk);
        dst += chunk;
        remaining -= chunk;
        page_index++;
    }

    while (remaining >= page)
    {
        int ret = nfc_read_page_pio(priv, (uint32_t)page_index, 0, dst, page);
        if (ret < 0)
            return ret;
        dst += page;
        remaining -= page;
        page_index++;
    }

    if (remaining)
    {
        uint8_t tmp[4096];
        if (page > sizeof(tmp))
            return -EINVAL;
        int ret = nfc_read_page_pio(priv, (uint32_t)page_index, 0, tmp, page);
        if (ret < 0)
            return ret;
        memcpy(dst, tmp, remaining);
        remaining = 0;
    }

    return (ssize_t)nbytes;
}

static int fmsh_mtd_ioctl(struct mtd_dev_s *dev, int cmd, unsigned long arg)
{
    struct fmsh_nand_priv *priv = container_of(dev, struct fmsh_nand_priv, mtd);

    switch (cmd)
    {
    case MTDIOC_GEOMETRY:
    {
        struct mtd_geometry_s *geo = (struct mtd_geometry_s *)(uintptr_t)arg;
        if (!geo)
            return -EINVAL;
        *geo = priv->geometry;
        return 0;
    }
    case BIOC_FLUSH:
        return 0;
    default:
        return -ENOTTY;
    }
}

static int fmsh_mtd_isbad(struct mtd_dev_s *dev, off_t block)
{
    struct fmsh_nand_priv *priv = container_of(dev, struct fmsh_nand_priv, mtd);
    uint32_t eb = (uint32_t)block; /* erase-block index */
    if (!priv->bbm_bitmap)
        return 0;
    int bad = (priv->bbm_bitmap[eb >> 3] >> (eb & 7)) & 1;
    if (!bad)
    {
        /* fallback 一次 OOB 判定 */
        uint8_t oob[64];
        uint32_t page0 = eb * priv->pages_per_erase;
        if (nfc_read_page_pio(priv, page0, priv->geometry.blocksize, oob, sizeof(oob)) == 0)
        {
            bad = (oob[0] != 0xFF);
            if (bad)
                priv->bbm_bitmap[eb >> 3] |= (1u << (eb & 7));
        }
    }
    return bad;
}

static int fmsh_oob_prog_bbm(struct fmsh_nand_priv *priv, uint32_t eb)
{
    /* 仅将该块第0页 OOB[0] 清0：先读整页OOB，再用随机编程写入1->0，不改其他位 */
    uint8_t oob[64];
    uint32_t page0 = eb * priv->pages_per_erase;
    int ret = nfc_read_page_pio(priv, page0, priv->geometry.blocksize, oob, sizeof(oob));
    if (ret < 0)
        return ret;
    if (oob[0] == 0x00)
        return 0; /* 已是坏标记 */
    oob[0] &= 0x00;
    /* 使用随机数据输入到 OOB：设置列到 OOB 起始，大小1字节，写入0x00，然后提交PROGRAM END */
    nfc_writel(priv, priv->geometry.blocksize, ADDR0_COL_REG);
    nfc_writel(priv, page0, ADDR0_ROW_REG);
    nfc_writel(priv, GEN_SEQ_CTRL_WRITE_PAGE_5CYCLE, GEN_SEQ_CTRL_REG);
    nfc_writel(priv, 4, DATA_SIZE_REG); /* 对齐到4字节写一个字 */
    nfc_fifo_flush(priv);
    nfc_clear_ints(priv);
    nfc_writel(priv, COMMAND_WRITE_PAGE_FIFO_GEN, COMMAND_REG);
    uint32_t val = 0x00000000; /* 低字节为 0x00 */
    nfc_writel(priv, val, FIFO_DATA_REG);
    ret = nfc_wait_bits(priv, INT_STATUS_REG, INT_STATUS_CMD_END_INT_FL, 1, 1000000);
    if (ret < 0)
        return ret;
    ret = nfc_wait_bits(priv, INT_STATUS_REG, INT_STATUS_MEM0_RDY_INT_FL, 1, 1000000);
    if (ret < 0)
        return ret;
    return 0;
}

static int fmsh_mtd_markbad(struct mtd_dev_s *dev, off_t block)
{
    struct fmsh_nand_priv *priv = container_of(dev, struct fmsh_nand_priv, mtd);
    uint32_t eb = (uint32_t)block;
    /* 仅允许 rootfs 范围内标坏 */
    if (!(eb >= priv->rootfs_efirst && eb < priv->rootfs_eend))
    {
        KLOG_W("BBM: reject markbad eb=%u (protected region)", eb);
        return -EPERM;
    }
    int ret = fmsh_oob_prog_bbm(priv, eb);
    if (ret == 0)
    {
        priv->bbm_bitmap[eb >> 3] |= (1u << (eb & 7));
        KLOG_I("BBM: markbad eb=%u (OOB[0]=0x00)", eb);
    }
    return ret;
}

static void fmsh_fill_default_geometry(struct fmsh_nand_priv *priv)
{
    /* Defaults from user-provided U-Boot nand info: 512MiB, 2KiB page, 128KiB erase, 64B OOB */
    const uint32_t page = 2048;                        /* bytes */
    const uint32_t erase = 128 * 1024;                 /* bytes */
    const uint64_t total = 512ULL * 1024ULL * 1024ULL; /* bytes */

    memset(&priv->geometry, 0, sizeof(priv->geometry));
    priv->geometry.blocksize = page;
    priv->geometry.erasesize = erase;
    priv->geometry.neraseblocks = (uint32_t)(total / erase);
    strlcpy(priv->geometry.model, "fmsh-nand", sizeof(priv->geometry.model));

    priv->pages_per_erase = erase / page;
    priv->oobsize = 64; /* bytes per page OOB */
}

static void fmsh_force_pio_mode(struct fmsh_nand_priv *priv)
{
    /* 强制 PIO：设置 SIU/FIFO 方向掩码，禁用 DMA 相关中断 */
    priv->use_dma = 0;
    /* Enable data-reg/command interrupts as needed */
    nfc_writel(priv, INT_MASK_CMD_END_INT_EN | INT_MASK_DATA_REG_EN | INT_MASK_MEM0_RDY_INT_EN,
               INT_MASK_REG);
    /* Clear any prior status */
    nfc_writel(priv,
               INT_STATUS_ECC_INT0_FL | INT_STATUS_STAT_ERR_INT0_FL | INT_STATUS_MEM0_RDY_INT_FL |
                   INT_STATUS_DMA_INT_FL | INT_STATUS_DATA_REG_FL | INT_STATUS_CMD_END_INT_FL,
               INT_STATUS_REG);
    /* 初始：不开 ECC，后续按 DTS 配置再启用；块大小按每擦除块包含的页数设置（本芯片为64页/块） */
    uint32_t ctrl = CONTROL_IO_WIDTH_8 | CONTROL_BLOCK_SIZE_64 | CONTROL_ECC_BLOCK_SIZE_512;
    nfc_writel(priv, ctrl, CONTROL_REG);

    /* Default generic sequence timing for read-page. Use 5-cycle addressing */
    nfc_writel(priv, GEN_SEQ_CTRL_READ_PAGE_5CYCLE, GEN_SEQ_CTRL_REG);
}

static void fmsh_config_ecc(struct fmsh_nand_priv *priv)
{
    /* 根据 priv->ecc_enabled/strength/step 配置硬件 ECC，并确保 OOB[0] 作为 BBM 不被覆盖 */
    uint32_t ctrl = CONTROL_IO_WIDTH_8 | CONTROL_BLOCK_SIZE_128;
    switch (priv->ecc_step)
    {
    case 256:
        ctrl |= CONTROL_ECC_BLOCK_SIZE_256;
        break;
    case 1024:
        ctrl |= CONTROL_ECC_BLOCK_SIZE_1024;
        break;
    case 512:
    default:
        ctrl |= CONTROL_ECC_BLOCK_SIZE_512;
        break;
    }
    if (priv->ecc_enabled)
        ctrl |= CONTROL_ECC_EN;
    nfc_writel(priv, ctrl, CONTROL_REG);

    if (priv->ecc_enabled)
    {
        /* 设置纠错能力与阈值；并计算 ECC 放置在 OOB 尾部的偏移，避免覆盖 OOB[0]（BBM） */
        uint32_t ecc_cap;
        switch (priv->ecc_strength)
        {
        case 2:
            ecc_cap = ECC_CTRL_ECC_CAP_2;
            break;
        case 4:
            ecc_cap = ECC_CTRL_ECC_CAP_4;
            break;
        case 16:
            ecc_cap = ECC_CTRL_ECC_CAP_16;
            break;
        case 24:
            ecc_cap = ECC_CTRL_ECC_CAP_24;
            break;
        case 32:
            ecc_cap = ECC_CTRL_ECC_CAP_32;
            break;
        case 8:
        default:
            ecc_cap = ECC_CTRL_ECC_CAP_8;
            break;
        }
        nfc_writel(priv, (ECC_CTRL_ECC_THRESHOLD(4) | ecc_cap), ECC_CTRL_REG);

        /* 估算每 step 的 ECC 冗余字节：strength<=4 -> 8B；否则 14B（与 U-Boot 一致） */
        uint32_t steps = priv->geometry.blocksize / (uint32_t)priv->ecc_step;
        uint32_t ecc_bytes_per_step = (priv->ecc_strength <= 4) ? 8u : 14u;
        uint32_t total_ecc_bytes = steps * ecc_bytes_per_step;
        /* 放到 OOB 尾部 */
        uint32_t ecc_off = priv->geometry.blocksize + priv->oobsize - total_ecc_bytes;
        nfc_writel(priv, ecc_off, ECC_OFFSET_REG);
        KLOG_I("ECC: step=%d strength=%d total=%d ecc_off=%u", priv->ecc_step, priv->ecc_strength,
               total_ecc_bytes, ecc_off);
    }
}

static int fmsh_register_partitions(struct fmsh_nand_priv *priv)
{
    /* Create MTD partitions matching U-Boot mtdparts:
     * 2m(fsbl), 2m(uboot), 1m(env), -(rootfs)
     * Units of firstblock/nblocks are in read/write blocks (page-sized blocks). */
    const uint32_t page = priv->geometry.blocksize;
    const uint32_t total_pages = (priv->geometry.neraseblocks * priv->geometry.erasesize) / page;

    off_t fsbl_first = 0;
    off_t fsbl_pages = (2 * 1024 * 1024) / page; /* 2MiB */

    off_t uboot_first = fsbl_first + fsbl_pages;
    off_t uboot_pages = (2 * 1024 * 1024) / page; /* 2MiB */

    off_t env_first = uboot_first + uboot_pages;
    off_t env_pages = (1 * 1024 * 1024) / page; /* 1MiB */

    off_t rootfs_first = env_first + env_pages;
    off_t rootfs_pages = total_pages - rootfs_first;

    /* Save rootfs erase-block range for markbad policy */
    priv->rootfs_efirst = (uint32_t)(rootfs_first / priv->pages_per_erase);
    priv->rootfs_eend = (uint32_t)(((rootfs_first + rootfs_pages) / priv->pages_per_erase));

    int ret = 0;
    extern int register_partition_with_mtd(const char *, mode_t, struct mtd_dev_s *, off_t, off_t);
    ret = register_partition_with_mtd("/dev/mtdfsbl", 0440, &priv->mtd, fsbl_first, fsbl_pages);
    if (ret < 0)
    {
        KLOG_E("register fsbl failed: %d", ret);
        return ret;
    }
    ret = register_partition_with_mtd("/dev/mtdu-boot", 0440, &priv->mtd, uboot_first, uboot_pages);
    if (ret < 0)
    {
        KLOG_E("register uboot failed: %d", ret);
        return ret;
    }
    ret = register_partition_with_mtd("/dev/mtdenv", 0440, &priv->mtd, env_first, env_pages);
    if (ret < 0)
    {
        KLOG_E("register env failed: %d", ret);
        return ret;
    }
    ret =
        register_partition_with_mtd("/dev/mtdrootfs", 0660, &priv->mtd, rootfs_first, rootfs_pages);
    if (ret < 0)
    {
        KLOG_E("register rootfs failed: %d", ret);
        return ret;
    }

    return 0;
}

static int fmsh_nand_probe(struct device *dev)
{
    struct fmsh_nand_priv *priv = calloc(1, sizeof(*priv));
    if (!priv)
        return -ENOMEM;

    /* Map registers */
    devaddr_region_t region;
    if (platform_get_resource_regs(dev, &region, 1) == 0)
    {
        priv->regs = (void *)region.vaddr;
    }

    /* Force PIO */
    fmsh_force_pio_mode(priv);

    /* Basic init: select bank0, enable CE, unprotect WP */
    nfc_writel(priv, MEM_CTRL_BANK_SEL(0) | MEM_CTRL_MEM_CE(0), MEM_CTRL_REG);

    /* Initialize geometry */
    fmsh_fill_default_geometry(priv);

    /* Parse ECC dts, but TEMPORARILY FORCE ECC OFF for diagnosis */
    const char *ecc_mode = dev_read_string(dev, "nand-ecc-mode");
    uint32_t ecc_strength = 0, ecc_step = 0;
    of_property_read_u32(dev->of_node, "nand-ecc-strength", &ecc_strength);
    of_property_read_u32(dev->of_node, "nand-ecc-step-size", &ecc_step);
    priv->ecc_enabled = 0; /* force off for diagnosis */
    priv->ecc_strength = ecc_strength ? (int)ecc_strength : 8;
    priv->ecc_step = ecc_step ? (int)ecc_step : 512;
    priv->uncorr_threshold = 3; /* 默认3次，可配置 */
    fmsh_config_ecc(priv);

    /* Allocate BBM bitmap & error counters */
    size_t eblocks = priv->geometry.neraseblocks;
    priv->bbm_bitmap = calloc((eblocks + 7) / 8, 1);
    priv->uncorr_cnt = calloc(eblocks, sizeof(uint16_t));

    /* Scan BBM once */
    for (uint32_t eb = 0; eb < eblocks; ++eb)
    {
        uint32_t page0 = eb * priv->pages_per_erase;
        uint8_t oob[64];
        if (nfc_read_page_pio(priv, page0, priv->geometry.blocksize, oob, sizeof(oob)) == 0)
        {
            if (oob[0] != 0xFF)
            {
                priv->bbm_bitmap[eb >> 3] |= (1u << (eb & 7));
            }
        }
    }
    KLOG_I("BBM: scan done");

    /* Hook mtd ops */
    memset(&priv->mtd, 0, sizeof(priv->mtd));
    priv->mtd.erase = fmsh_mtd_erase;
    priv->mtd.bread = fmsh_mtd_bread;
    priv->mtd.bwrite = fmsh_mtd_bwrite;
    priv->mtd.read = fmsh_mtd_read;
    priv->mtd.ioctl = fmsh_mtd_ioctl;
    priv->mtd.isbad = fmsh_mtd_isbad;
    priv->mtd.markbad = fmsh_mtd_markbad;
    priv->mtd.name = "fmsh-nand";

    /* Register main MTD node */
    int ret = register_mtddriver("/dev/mtd0", &priv->mtd, 0660, priv);
    if (ret < 0)
    {
        KLOG_E("register_mtddriver failed: %d", ret);
        free(priv->uncorr_cnt);
        free(priv->bbm_bitmap);
        free(priv);
        return ret;
    }

    /* Register fixed partitions matching U-Boot layout */
    ret = fmsh_register_partitions(priv);
    if (ret < 0)
    {
        KLOG_E("partition registration failed: %d", ret);
        /* Keep /dev/mtd0 registered; partitions may be optional */
    }

    dev->priv = priv;
    KLOG_I("FMSH NAND MTD registered: page=%u, erase=%u, blocks=%u, mode=%s, ecc=%s/%dper%d",
           priv->geometry.blocksize, priv->geometry.erasesize, priv->geometry.neraseblocks,
           priv->use_dma ? "dma" : "pio", priv->ecc_enabled ? "hw" : "off", priv->ecc_strength,
           priv->ecc_step);

    return 0;
}

static int fmsh_nand_remove(struct device *dev)
{
    /* TODO: unregister devices if needed */
    if (dev && dev->priv)
    {
        free(dev->priv);
        dev->priv = NULL;
    }
    return 0;
}

static struct of_device_id fmsh_nand_match[] = {
    {.compatible = "fmsh,psoc-nfc"},
    {/* end of list */},
};

static struct driver fmsh_nand_driver = {
    .name = "fmsh-nand-mtd",
    .probe = fmsh_nand_probe,
    .remove = fmsh_nand_remove,
    .match_table = fmsh_nand_match,
    .priv_auto_alloc_size = 0,
};

static int fmsh_nand_driver_init(void)
{
    return platform_add_driver(&fmsh_nand_driver);
}
INIT_EXPORT_DRIVER(fmsh_nand_driver_init, "FMSH NAND MTD driver");
