// SPDX-License-Identifier: GPL-2.0+
#if !defined(TTOS_ENABLE_UBOOT_FMSH_NFC)
/* File disabled for TTOS build until fully ported. */
void __ttos_dummy_fmsh_nfc(void) {}
#else

/*
 * FMSH PSOC NAND flash driver
 *
 * Based on code from LTIB:
 * CADENCE NFC NAND Flash Driver
 *
 * Copyright (C) 2018 FMSH, Inc.
 */

#include "fmsh_nfc.h"
/* Exclude U-Boot specific headers when building under TTOS */
#ifdef __UBOOT__
#include <asm/io.h>
#include <bouncebuf.h>
#include <clk-uclass.h>
#include <common.h>
#include <dm.h>
#include <fdtdec.h>
#include <linux/io.h>
#include <linux/ioport.h>
#include <memalign.h>
#include <nand.h>
DECLARE_GLOBAL_DATA_PTR;
#endif

#undef UPDATE_NFC_TIMING

static struct nand_chip nand_chip[CONFIG_SYS_MAX_NAND_DEVICE];

#ifdef UPDATE_NFC_TIMING
const struct cad_timings default_mode0_pll_enabled = {
    0x0c140e00, 0x00140414, 0x00000030, 0x00000000, 0x00000000, 0x00000000, 0x00000000};

static struct nfc_timing default_nfc_timing = {
    .twhr = 60,
    .trhw = 100,
    .tadl = 70,
    .tccs = 0,
    .tww = 100,
    .trr = 20,
    .twb = 200,
    .trwh = 35,
    .trwp = 50,
};
#endif

static inline uint32_t cad_read(struct cadence_nfc *priv, u32 reg_offset)
{
    return __raw_readl((u32)priv->regs + reg_offset);
}

static inline void cad_write(struct cadence_nfc *priv, uint32_t data, uint reg_offset)
{
    __raw_writel(data, (u32)priv->regs + reg_offset);
}

/* Write per-chip specific config to controller */
void cad_config(struct cadence_nfc *priv, void *ref)
{
    static void *saved_ref;
    struct cad_config *cad_config = &priv->config;

    /* To avoid rewriting these unnecessarily every time, we only do
     * it when the ref has changed, or if ref == NULL (=> force). */
    if (ref)
    {
        if (ref == saved_ref)
        {
            return;
        }
        saved_ref = ref; /* only save if non-null */
    }

    cad_write(priv, cad_config->mem_ctrl, MEM_CTRL_REG);
    cad_write(priv, cad_config->control, CONTROL_REG);
    cad_write(priv, cad_config->ecc_ctrl, ECC_CTRL_REG);
    cad_write(priv, cad_config->ecc_offset, ECC_OFFSET_REG);
}

/* Set up CONTROL depending on whether we want ECC or not */
static void cad_setup_control(struct cadence_nfc *priv, int enable_ecc)
{
    uint32_t control;

    /* When reading the oob, we never want ECC, when reading the
     * main area, it depends. */
    control = cad_read(priv, CONTROL_REG) & ~CONTROL_ECC_EN;
    if (enable_ecc)
    {
        control |= CONTROL_ECC_EN;
    }

    cad_write(priv, control, CONTROL_REG);
}

/* Set up interrupt, send command */
static void cad_command(struct cadence_nfc *priv, uint32_t cmd)
{
    int retry = 0;
    do
    {
        cad_write(priv, 0, INT_STATUS_REG); // 0x14
        cad_write(priv, cmd, COMMAND_REG);  // 0x00
        pr_debug("Send command: cmd=0x%x \n", cmd);
        if (retry++ >= 3)
            break;
    } while (cmd != cad_read(priv, COMMAND_REG));
}

/* wait for (any bit of) expected state */
static void cad_wait_status(struct cadence_nfc *priv, uint32_t reg, uint32_t status)
{
    int cmd_loops = 0;
    uint32_t read_status;

    /* Wait for status */
    pr_debug("Waiting for 0x%08x bit(s) to be set in ", status);
    if (reg == INT_STATUS_REG)
        pr_debug("int_status\n");
    else if (reg == STATUS_REG)
        pr_debug("status\n");
    else if (reg == DMA_CTRL_REG)
        pr_debug("dma_status\n");
    else if (reg == FIFO_STATE_REG)
        pr_debug("fifo_status\n");
    else
        pr_debug("wrong register, check your code!\n");

    do
    {
        cmd_loops++;
        read_status = cad_read(priv, reg);
        pr_debug("Wait time(%d): status=0x%08x \n", cmd_loops, read_status);
        udelay(1);
    } while (!(read_status & status) && cmd_loops < MAX_CMD_LOOPS);

    if (cmd_loops >= MAX_CMD_LOOPS)
    {
        pr_info("wait status 0x%08x timed out after %d loops: "
                "STATUS=0x%08x\n",
                status, cmd_loops, read_status);
    }

    if (reg == INT_STATUS_REG)
        cad_write(priv, 0, INT_STATUS_REG); // 0x14
}

/* Set up interrupt, send command, then wait for (any bit of) expected state */
/*
 * Before issuing a command, we should check if the controller is ready.
 * We can't check INT_STATUS_REG.MEM0_RDY_INT_FL as it is not a status bit,
 * it is set on an nfc state transition after the completion of for
 * instance a page program command, so we can use it as a command
 * completed trigger however.
 * (See NFC Design Spec (rev 1.15) figure 35 for illustration.)
 * TODO: However, we could check STATUS.CTRL_STAT, which should always
 * be 0 prior to issuing a command, indicating the controller is not
 * busy.
 * */
static void cad_command_and_wait(struct cadence_nfc *priv, uint32_t cad_command, uint32_t int_state)
{
    int cmd_loops = 0;
    uint32_t read_int_status;
    uint32_t read_status, dma_status;
    int retry = 0;

    do
    {
        /* Clear interrupt status bits */
        cad_write(priv, 0, INT_STATUS_REG);        // 0x14
        cad_write(priv, cad_command, COMMAND_REG); // 0x00
        pr_debug("----------- cad_command = 0x%x \n", cad_command);
        if (retry++ >= 3)
            break;
    } while (cad_command != cad_read(priv, COMMAND_REG));

    /* Wait for command to complete */
    pr_debug("Waiting for 0x%08x bit(s) to be set in int_status\n", int_state);
    do
    {
        cmd_loops++;
        read_int_status = cad_read(priv, INT_STATUS_REG);

        read_status = cad_read(priv, STATUS_REG);
        dma_status = cad_read(priv, DMA_CTRL_REG);
        pr_debug("Wait for command done: 0x%08x/0x%08x/0x%08x (%d)\n", read_status, read_int_status,
                 dma_status, cmd_loops);
        udelay(1);
        if (0 == int_state)
            break;

        //		if (read_status&0x01) {
        //			break;
        //		}
        //	} while (cmd_loops < MAX_CMD_LOOPS);
    } while (!(read_int_status & int_state) && (cmd_loops < MAX_CMD_LOOPS));

    if (cmd_loops >= MAX_CMD_LOOPS)
    {
        pr_info("Int wait for 0x%08x timed out after %d loops: "
                "STATUS = 0x%08x, INT_STATUS=0x%08x, "
                "DMA_CTRL = 0x%08x, command 0x%08x\n",
                int_state, cmd_loops, read_status, read_int_status, dma_status, cad_command);
    }
    cad_write(priv, 0, INT_STATUS_REG); // 0x14
}

static void cad_init_siu_fifo(struct cadence_nfc *priv, int bytes)
{
    cad_write(priv, (bytes + 3) & 0xfffffffc, DATA_SIZE_REG);
    cad_write(priv, FIFO_INIT_FIFO_INIT, FIFO_INIT_REG); /* Flush FIFO */
}

/* Initialize transfer to or from DMA buffer */
static void cad_init_dmabuf(struct cadence_nfc *priv, int bytes)
{
    priv->dma.ptr = priv->dma.buf;
    priv->dma.bytes_left = bytes;
    pr_debug("Init dma buf: buf=0x%p, left bytes=%d \n", priv->dma.ptr, priv->dma.bytes_left);
}

/*dma ddr workaroud*/
//#define dma_virt_to_phy(x)	((((unsigned long)(x)>0x80000000) && ((unsigned
//long)(x)<0xc0000000))?
//((unsigned long)(x)-0x80000000):(unsigned long)(x))
#define dma_virt_to_phy(x) (x)

/* Initialize DMA, wq and interrupt status for upcoming transfer. */
static void cad_init_dma(struct cadence_nfc *priv, uint64_t addr, int bytes)
{
    int dma_trig_level;

    /* DMA control */
    /* Start when COMMAND register written, set burst type/size */
    // cad_write(priv, DMA_CTRL_DMA_START | DMA_CTRL_DMA_BURST_I_P_4, DMA_CTRL_REG);
    cad_write(priv, DMA_CTRL_DMA_START | DMA_CTRL_DMA_BURST_SINGLE, DMA_CTRL_REG);

    /* DMA address and length */
#ifdef EVATRONIX_DMA64BIT
    /* The manual says this register does not 'occur' (sic) unless
     * 64 bit DMA support is included. */
    cad_write(priv, addr >> 32, DMA_ADDR_H_REG);
#endif
    cad_write(priv, dma_virt_to_phy(addr), DMA_ADDR_L_REG);

    /* Byte counter */
    /* Round up to nearest 32-bit word */
    cad_write(priv, (bytes + 3) & 0xfffffffc, DMA_CNT_REG);

    /* Cap DMA trigger level at FIFO size */
    dma_trig_level = bytes * 8 / 32; /* 32-bit entities */
    if (dma_trig_level > DMA_TLVL_MAX)
    {
        dma_trig_level = DMA_TLVL_MAX;
    }
    cad_write(priv, dma_trig_level, DMA_TLVL_REG);

    pr_debug("Init dma ctrl: dma ctrl=0x%08x, dma addr=0x%08x, dma cnt=0x%08x, dma lvl=0x%08x \n",
             cad_read(priv, DMA_CTRL_REG), cad_read(priv, DMA_ADDR_L_REG),
             cad_read(priv, DMA_CNT_REG), cad_read(priv, DMA_TLVL_REG));
}

static inline u32 cad_read_siu_fifo(struct cadence_nfc *priv)
{
    return __raw_readl((u32)priv->regs + FIFO_DATA_REG);
}

static inline u32 cad_write_siu_fifo(struct cadence_nfc *priv, u32 val)
{
    return __raw_writel(val, (u32)priv->regs + FIFO_DATA_REG);
}

static u32 cad_read_fifo(struct cadence_nfc *priv)
{
    u32 val = 0;

    if (priv->cmd_cache.command != NAND_CMD_STATUS)
    {
        val = cad_read_siu_fifo(priv);
    }

    return val;
}

static void cad_read_fifobuf(struct cadence_nfc *priv, u32 *buf, int len)
{
    int i;

    pr_debug("read fifo buf p:%p, buf %p, len %d\n", priv, buf, len);
    if (len > priv->dma.bytes_left)
    {
        pr_debug("read fifo len(%d) > data size%d\n", len, priv->dma.bytes_left);
        return;
    }

    for (i = 0; i < len / 4;)
    {
        if (!(cad_read(priv, FIFO_STATE_REG) & FIFO_STATE_DF_R_EMPTY))
        {
            *buf = cad_read_fifo(priv);
            buf++;
            i++;
        }
    }
}

static void cad_read_mode(struct cadence_nfc *priv, int page, int column, enum cad_read_mode m)
{
    struct nand_chip *nand = &nand_chip[priv->cs];
    struct mtd_info *mtd = nand_to_mtd(nand);
    int size;
    uint32_t command = 0;
    uint32_t cmd = 0;

    switch (m)
    {
    case CAD_READ_OOB:
        size = mtd->oobsize;
        break;
    case CAD_READ_ALL:
        size = mtd->oobsize + mtd->writesize;
        break;
    case CAD_READ_STD:
    case CAD_READ_RAW:
        size = mtd->writesize;
        break;
    default:
        size = 0;
        break;
    }

    /* Set up ECC depending on mode */
    cad_setup_control(priv, m == CAD_READ_STD);

    /* Set up DMA and transfer size */
    cad_init_dmabuf(priv, size);
    if (priv->use_mode == NFC_INTERNAL_DMA)
    {
        cad_init_dma(priv, priv->dma.phys, size);
        flush_dcache_range(ALIGN_DOWN((unsigned long)priv->dma.phys, 64),
                           ALIGN((unsigned long)priv->dma.phys + size, 64));
    }

    cad_write(priv, size, DATA_SIZE_REG);

    /* Set up addresses */
    if (m == CAD_READ_OOB)
    {
        column += mtd->writesize;
    }

    if (nand->options & SP_OPTIONS16)
        column >>= 1;
    cad_write(priv, column, ADDR0_COL_REG);
    cad_write(priv, page, ADDR0_ROW_REG);

    /* For devices > 128 MiB we have 5 address cycles and can use a
     * standard NFC command sequence. For smaller devices we have
     * 4 address cycles and need to use a Generic Command Sequence. */
    if (nand->chipsize > (128 << 20))
    {
        cmd = GEN_SEQ_CTRL_READ_PAGE_5CYCLE;
    }
    else
    {
        cmd = GEN_SEQ_CTRL_READ_PAGE_4CYCLE;
    }
    if (priv->use_mode == NFC_INTERNAL_DMA)
    {
        cad_write(priv, cmd, GEN_SEQ_CTRL_REG);
        cad_write(priv, FIFO_INIT_FIFO_INIT, FIFO_INIT_REG); /* Flush FIFO */
        command = COMMAND_READ_PAGE_DMA_STD;
    }
    else if (priv->use_mode == NFC_SIU_FIFO)
    {
        cad_write(priv, cmd, GEN_SEQ_CTRL_REG);
        cad_write(priv, FIFO_INIT_FIFO_INIT, FIFO_INIT_REG); /* Flush FIFO */
        command = COMMAND_READ_PAGE_STD;
    }

    pr_debug("Command info: row addr=0x%08x, column addr=0x%08x, data size=0x%08x \n",
             cad_read(priv, ADDR0_ROW_REG), cad_read(priv, ADDR0_COL_REG),
             cad_read(priv, DATA_SIZE_REG));

    if (priv->use_mode == NFC_INTERNAL_DMA)
    {
        // cad_command_and_wait(priv, command, INT_STATUS_DMA_INT_FL);
        cad_command(priv, command);
        cad_wait_status(priv, FIFO_STATE_REG, FIFO_STATE_CF_EMPTY);
        cad_wait_status(priv, STATUS_REG, STATUS_MEM0_ST);
        cad_wait_status(priv, DMA_CTRL_REG, DMA_CTRL_DMA_READY);
        invalidate_dcache_range(ALIGN_DOWN((unsigned long)priv->dma.phys, 64),
                                ALIGN((unsigned long)priv->dma.phys + size, 64));
    }
    else if (priv->use_mode == NFC_SIU_FIFO)
    {
        cad_command_and_wait(priv, command, INT_STATUS_CMD_END_INT_FL);
    }
}

static void cad_write_mode(struct cadence_nfc *priv, int page, int column, int oob, int raw)
{
    int size;
    u32 cmd = 0, command = 0;
    u8 *write_ptr;
    struct nand_chip *nand = &nand_chip[priv->cs];
    struct mtd_info *mtd = nand_to_mtd(nand);

    /* Since the controller handles ECC on its own, raw mode doesn't
     * come into the size calculations. */
    cad_setup_control(priv, !raw);

    /* For devices > 128 MiB we have 5 address cycles and can use a
     * standard NFC command sequence. For smaller devices we have
     * 4 address cycles and need to use a Generic Command Sequence. */
    if (nand->chipsize > (128 << 20))
    {
        cmd = GEN_SEQ_CTRL_WRITE_PAGE_5CYCLE;
    }
    else
    {
        cmd = GEN_SEQ_CTRL_WRITE_PAGE_4CYCLE;
    }

    if (priv->use_mode == NFC_INTERNAL_DMA)
    {
        cad_write(priv, cmd, GEN_SEQ_CTRL_REG);
        command = COMMAND_WRITE_PAGE_DMA_STD;
    }
    else if (priv->use_mode == NFC_SIU_FIFO)
    {
        cad_write(priv, cmd, GEN_SEQ_CTRL_REG);
        command = COMMAND_WRITE_PAGE_FIFO_GEN;
    }
    cad_write(priv, FIFO_INIT_FIFO_INIT, FIFO_INIT_REG); /* Flush FIFO */

    write_ptr = priv->dma.buf;
    if (column < mtd->writesize)
    {
        /* page data write only */
        size = mtd->writesize;
        /* Set up DMA and transfer size */
        if (priv->use_mode == NFC_INTERNAL_DMA)
        {
            cad_init_dma(priv, (u32)write_ptr, size);
            flush_dcache_range(ALIGN_DOWN((unsigned long)write_ptr, 64),
                               ALIGN((unsigned long)write_ptr + size, 64));
        }
        cad_write(priv, size, DATA_SIZE_REG);
        /* Set up addresses */
        if (nand->options & SP_OPTIONS16)
            column >>= 1;
        cad_write(priv, column, ADDR0_COL_REG);
        cad_write(priv, page, ADDR0_ROW_REG);

        if (priv->use_mode == NFC_INTERNAL_DMA)
        {
            cad_command(priv, command);
            cad_wait_status(priv, DMA_CTRL_REG, DMA_CTRL_DMA_READY);
            cad_wait_status(priv, INT_STATUS_REG,
                            INT_STATUS_CMD_END_INT_FL | INT_STATUS_MEM0_RDY_INT_FL);
            invalidate_dcache_range(ALIGN_DOWN((unsigned long)write_ptr, 64),
                                    ALIGN((unsigned long)write_ptr + size, 64));
        }
        else if (priv->use_mode == NFC_SIU_FIFO)
        {
            int i;
            for (i = 0; i < size;)
            {
                if (!(cad_read(priv, FIFO_STATE_REG) & FIFO_STATE_DF_W_FULL))
                {
                    cad_write_siu_fifo(priv, readl((void *)write_ptr));
                    write_ptr += 0x4;
                    i = i + 4;
                }
            } /* end for */
            cad_command(priv, command);
            cad_wait_status(priv, INT_STATUS_REG,
                            INT_STATUS_CMD_END_INT_FL | INT_STATUS_MEM0_RDY_INT_FL);
        } /* end else if NFC_SIU_FIFO */
        column += mtd->writesize;
        write_ptr += mtd->writesize;
    }

    if (oob)
    {
        size = mtd->oobsize;
        /* Set up DMA and transfer size */
        if (priv->use_mode == NFC_INTERNAL_DMA)
        {
            cad_init_dma(priv, (u32)write_ptr, size);
            flush_dcache_range(ALIGN_DOWN((unsigned long)write_ptr, 64),
                               ALIGN((unsigned long)write_ptr + size, 64));
        }
        cad_write(priv, size, DATA_SIZE_REG);
        /* Set up addresses */
        if (nand->options & SP_OPTIONS16)
            column >>= 1;
        cad_write(priv, column, ADDR0_COL_REG);
        cad_write(priv, page, ADDR0_ROW_REG);

        if (priv->use_mode == NFC_INTERNAL_DMA)
        {
            cad_command(priv, command);
            cad_wait_status(priv, DMA_CTRL_REG, DMA_CTRL_DMA_READY);
            cad_wait_status(priv, INT_STATUS_REG,
                            INT_STATUS_CMD_END_INT_FL | INT_STATUS_MEM0_RDY_INT_FL);
            invalidate_dcache_range(ALIGN_DOWN((unsigned long)write_ptr, 64),
                                    ALIGN((unsigned long)write_ptr + size, 64));
        }
        else if (priv->use_mode == NFC_SIU_FIFO)
        {
            int i;
            for (i = 0; i < size;)
            {
                if (!(cad_read(priv, FIFO_STATE_REG) & FIFO_STATE_DF_W_FULL))
                {
                    cad_write_siu_fifo(priv, readl((void *)write_ptr));
                    write_ptr += 0x4;
                    i = i + 4;
                }
            } /* end for */
            cad_command(priv, command);
            cad_wait_status(priv, INT_STATUS_REG,
                            INT_STATUS_CMD_END_INT_FL | INT_STATUS_MEM0_RDY_INT_FL);
        } /* end else if NFC_SIU_FIFO */
    }     /* end if oob */
}

/* Block erase */
static void cad_block_erase(struct cadence_nfc *priv, int page)
{
    /* Set up addresses */
    cad_write(priv, page, ADDR0_ROW_REG);
    pr_debug("Erase block containing page %d\n", page);

    /* Send 3 address cycle block erase command */
    cad_command_and_wait(priv, COMMAND_BLOCK_ERASE, INT_STATUS_MEM0_RDY_INT_FL);

    /* TODO: What to do if we get an error bit set here (i.e.INT_STATUS_REG.
     * STAT_ERR_INT0_FL) ? Normally, error status is checked by nand_base
     * by doing a status read after the erase command. So we can probably
     * ignore STAT_ERR_INT0_FL here. If need be, we can save the
     * status so a subsequent status right might use it for something.
     * The err bit probably just indicates that the flash didn't pull
     * R/_B low within tWB. */
}

static inline uint8_t cad_read_dmabuf_byte(struct cadence_nfc *priv)
{
    if (priv->dma.bytes_left)
    {
        priv->dma.bytes_left--;
        return *priv->dma.ptr++;
    }
    else
    {
        return 0; /* no data */
    }
}

static unsigned char cad_read_byte(struct cadence_nfc *priv)
{
    uint8_t status_value;

    if (priv->cmd_cache.command != NAND_CMD_STATUS)
    {
        return cad_read_dmabuf_byte(priv);
    }

    /* else it is read status command */
    pr_debug("Read status\n");

    /* In order to read status, we need to send a READ_STATUS command
     * to the NFC first, in order to get the data into the DATA_REG */
    /* Transfer to DATA_REG register */
    cad_write(priv, DATA_REG_SIZE_DATA_REG_SIZE(1), DATA_REG_SIZE_REG);

    /* We want to read all status bits from the device */
    //	cad_write(priv, STATUS_MASK_STATE_MASK(0xff), STATUS_MASK_REG); // don't care status_mask
    // reg
    cad_command_and_wait(priv, COMMAND_READ_STATUS, INT_STATUS_DATA_REG_FL);
    status_value = cad_read(priv, DATA_REG_REG) & 0xff;
    pr_debug("Read Status 0x%08x\n", status_value);

    return status_value;
}

/* Read block of data from DMA buffer */
static void cad_read_dmabuf(struct cadence_nfc *priv, uint8_t *buf, int len)
{
    pr_debug("Read dmabuf: src=0x%p, dst=0x%p, byte=%d \n", priv->dma.ptr, buf, len);
    if (len > priv->dma.bytes_left)
    {
        pr_info("Trying to read %d bytes with %d bytes remaining\n", len, priv->dma.bytes_left);
    }
    memcpy(buf, priv->dma.ptr, len);
    priv->dma.ptr += len;
    priv->dma.bytes_left -= len;
}

static void cad_read_buf(struct cadence_nfc *priv, unsigned char *buf, int len)
{
    if (priv->use_mode == NFC_INTERNAL_DMA)
        cad_read_dmabuf(priv, buf, len);
    else
        cad_read_fifobuf(priv, (u32 *)buf, len);
}

/* Write block of data to DMA buffer */
static void cad_write_dmabuf(struct cadence_nfc *priv, const uint8_t *buf, int len)
{
    pr_debug("Write dma buf: dst=0x%p, src=0x%p, len=%d\n", priv->dma.ptr, buf, len);
    /* TODO: Grab info pointer from mtd instead of using same always ? */
    if (len > priv->dma.bytes_left)
    {
        pr_info("Trying to write %d bytes with %d bytes remaining\n", len, priv->dma.bytes_left);
    }
    memcpy(priv->dma.ptr, buf, len);
    priv->dma.ptr += len;
    priv->dma.bytes_left -= len;
}

/* Read state of ready pin */
static inline int cad_dev_ready(struct cadence_nfc *priv)
{
    return !!(cad_read(priv, STATUS_REG) & STATUS_MEM_ST(priv->cs));
}

static int nfc_markbad(struct mtd_info *mtd, loff_t ofs)
{
    struct nand_chip *nand = mtd_to_nand(mtd);
    uint8_t buf[2] = {0, 0};
    int len, ooboffs;
    int page;
    int ret = 0, res, i = 0;

    ooboffs = nand->badblockpos;
    if (mtd->writesize > 512)
    {
        len = 2;
    }
    else
    {
        len = 1;
    }

    memset(nand->oob_poi, 0xff, mtd->oobsize);
    memcpy(nand->oob_poi + ooboffs, buf, len);
    memcpy(nand->oob_poi + 5, buf, 1);

    /* Write to first/last page(s) if necessary */
    if (nand->bbt_options & NAND_BBT_SCANLASTPAGE)
        ofs += mtd->erasesize - mtd->writesize;
    do
    {
        page = (int)(ofs >> nand->page_shift);
        res = nand->ecc.write_oob(mtd, nand, page);
        if (!ret)
            ret = res;

        i++;
        ofs += mtd->writesize;
    } while ((nand->bbt_options & NAND_BBT_SCAN2NDPAGE) && i < 2);

    return ret;
}

/* Do the dirty work for read_page_foo */
static int cad_read_page_mode(struct cadence_nfc *priv, uint8_t *buf, int oob_required, int page,
                              enum cad_read_mode m)
{
    unsigned int max_bitflips;
    uint32_t ecc_status;
    struct nand_chip *nand = &nand_chip[priv->cs];
    struct mtd_info *mtd = nand_to_mtd(nand);
    int stat;

    if (page != priv->cmd_cache.page)
    {
        pr_debug("Warning: Read page has different page number than "
                 "READ0: %d vs. %d\n",
                 page, priv->cmd_cache.page);
    }

    if (m == CAD_READ_STD)
    {
        /* ECC error flags and counters are not cleared automatically
         * so we do it here.
         */
        /* Note that the design spec says nothing about having to
         * zero ECC_STAT (although it explicitly says that ECC_CNT
         * needs to be zeroed by software), but testing on actual
         * hardware (RTL at this stage) reveals that this is in fact
         * the case.
         */
        cad_write(priv, 0, ECC_STAT_REG);
        cad_write(priv, 0, ECC_CNT_REG);
    }

    cad_read_mode(priv, priv->cmd_cache.page, priv->cmd_cache.column, m);
    nand->read_buf(mtd, buf, mtd->writesize);

    if (oob_required)
    {
        nand->cmdfunc(mtd, NAND_CMD_READOOB, 0, page);
        nand->read_buf(mtd, nand->oob_poi, mtd->oobsize);
    }

    if (m == CAD_READ_RAW)
    {
        return 0;
    }

    /* Get ECC status from controller */
    ecc_status = cad_read(priv, ECC_STAT_REG);
    max_bitflips = cad_read(priv, ECC_CNT_REG) & ECC_CNT_ERR_LVL_MASK;
    cad_write(priv, 0, ECC_STAT_REG);

#ifdef WORKAROUND_NO_ECC_CNT
    /* If we get an ERROR bit set, but ECC_CNT is 0, we assume
     * a single bit flip has occurred for want of better information. */
    if ((ecc_status & ECC_STAT_ERROR_0) && max_bitflips == 0)
    {
        max_bitflips = 1;
    }
#endif

    /*
     * the controler will correct the data, and retrun the result via
     * ECC_STAT register, we put whole page look as a single ECC block
     * unit
     * */
    if (ecc_status & (ECC_STAT_OVER_0 | ECC_STAT_UNC_0))
    {
        /* check for empty pages with bitflips */
        stat =
            nand_check_erased_ecc_chunk(buf, mtd->writesize, NULL, 0, NULL, 0, nand->ecc.strength);
        if (stat < 0)
        {
            mtd->ecc_stats.failed++;
            max_bitflips = 0;
        }
        else
        {
            /* empty pages */
            return 0;
        }
    }
    else
    {
        mtd->ecc_stats.corrected += max_bitflips;
    }

    pr_debug("ECC read status: %s%s%s%s, correction count %d\n",
             ecc_status & ECC_STAT_UNC_0 ? "Uncorrected " : "",
             ecc_status & ECC_STAT_ERROR_0 ? "Corrected " : "",
             ecc_status & ECC_STAT_OVER_0 ? "Over limit " : "",
             ecc_status & (ECC_STAT_UNC_0 | ECC_STAT_ERROR_0 | ECC_STAT_OVER_0) ? "" : "ok",
             max_bitflips);

    return max_bitflips;
}

/* Read page with HW ECC */
static int cad_read_page_hwecc(struct cadence_nfc *priv, uint8_t *buf, int oob_required, int page)
{
    return cad_read_page_mode(priv, buf, oob_required, page, CAD_READ_STD);
}

/* Read page with no ECC */
static int cad_read_page_raw(struct cadence_nfc *priv, uint8_t *buf, int oob_required, int page)
{
    return cad_read_page_mode(priv, buf, oob_required, page, CAD_READ_RAW);
}

/* Write page with HW ECC */
/* This is the only place where we know we'll be writing w/ ECC */
static int cad_write_page_hwecc(struct cadence_nfc *priv, const uint8_t *buf, int oob_required)
{
    struct nand_chip *nand = &nand_chip[priv->cs];
    struct mtd_info *mtd = nand_to_mtd(nand);

    priv->cmd_cache.oob_required = oob_required;
    priv->cmd_cache.write_raw = 0;

    /* A bit silly this, this is actually cad_write_dmabuf */
    cad_write_dmabuf(priv, buf, mtd->writesize);

    /* The controller can't write data to the oob when ECC is enabled,
     * so we set oob_required to 0 here and don't process the oob
     * further even if requested. This could happen for instance if
     * using nandwrite -o without -n . */
    if (oob_required)
    {
        cad_write_dmabuf(priv, nand->oob_poi, mtd->oobsize);
    }

    return 0;
}

/* Write page with no ECC */
/* This is the only place where we know we won't be writing w/ ECC */
static int cad_write_page_raw(struct cadence_nfc *priv, const uint8_t *buf, int oob_required)
{
    struct nand_chip *nand = &nand_chip[priv->cs];
    struct mtd_info *mtd = nand_to_mtd(nand);

    /* We need this for the upcoming PAGEPROG command */
    priv->cmd_cache.oob_required = oob_required;
    priv->cmd_cache.write_raw = 1;

    /* A bit silly this, this is actually cad_write_dmabuf */
    cad_write_dmabuf(priv, buf, mtd->writesize);

    /* The controller can't write data to the oob when ECC is enabled,
     * so we set oob_required to 0 here and don't process the oob
     * further even if requested. This could happen for instance if
     * using nandwrite -o without -n . */
    if (oob_required)
    {
        cad_write_dmabuf(priv, nand->oob_poi, mtd->oobsize);
    }

    return 0;
}

static int cad_write_oob(struct cadence_nfc *priv, int page)
{
    struct nand_chip *nand = &nand_chip[priv->cs];
    struct mtd_info *mtd = nand_to_mtd(nand);

    /* Initialize nfc dma */
    cad_init_dmabuf(priv, DMA_BUF_SIZE);
    cad_write_dmabuf(priv, nand->oob_poi, mtd->oobsize);
    /* write oob */
    cad_write_mode(priv, page, mtd->writesize, 1, 1);

    return 0;
}

static void cad_nand_command(struct cadence_nfc *priv, unsigned int command, int column,
                             int page_addr)
{
    int i;
    u32 val;

    /* Save command so that other parts of the API can figure out
     * what's actually going on. */
    priv->cmd_cache.command = command;

    /* Configure the NFC for the flash chip in question. */
    cad_config(priv, priv);

    /* Some commands we execute immediately, while some need to be
     * deferred until we have all the data needed, i.e. for page read,
     * we can't initiate the read until we know if we are going to be
     * using raw mode or not.
     */
    switch (command)
    {
    case NAND_CMD_READ0:
        pr_debug("READ0 page %d, column %d\n", page_addr, column);
        if (priv->setup.ecc_mode == NFC_ECC_HW)
        {
            /* We do not yet know if the caller wants to
             * read the page with or without ECC, so we
             * just store the page number and main/oob flag
             * here.
             * TODO: Since the page number arrives via the
             * read_page call, we don't really need to
             * store it.
             */
            priv->cmd_cache.page = page_addr;
            priv->cmd_cache.column = column;
        }
        else
        {
            /* Read the whole page including oob */
            priv->cmd_cache.oob_required = 1;
            cad_read_mode(priv, page_addr, column, CAD_READ_ALL);
        }
        break;
    case NAND_CMD_READOOB:
        pr_debug("READOOB page %d, column %d\n", page_addr, column);

        if (priv->use_mode == NFC_SIU_FIFO)
        {
            cad_read_mode(priv, page_addr, column, CAD_READ_OOB);
        }
        else if (priv->use_mode == NFC_INTERNAL_DMA)
        {
            /* In contrast to READ0, where nand_base always calls
             * a read_page_foo function before reading the data,
             * for READOOB, read_buf is called instead.
             * We don't want the actual read in read_buf, so
             * we put it here.
             */
            cad_read_mode(priv, page_addr, column, CAD_READ_OOB);
        }

        break;
    case NAND_CMD_ERASE1:
        pr_debug("ERASE1 page %d\n", page_addr);
        /* Just grab page parameter, wait until ERASE2 to do
         * something.
         */
        priv->cmd_cache.page = page_addr;
        break;
    case NAND_CMD_ERASE2:
        pr_debug("ERASE2 page %d, do it\n", priv->cmd_cache.page);
        /* Off we go! */
        cad_block_erase(priv, priv->cmd_cache.page);
        break;
    case NAND_CMD_RESET:
        pr_debug("Chip reset\n");
        cad_command_and_wait(priv, COMMAND_RESET, INT_STATUS_MEM0_RDY_INT_FL);
        break;
    case NAND_CMD_SEQIN:
        pr_debug("SEQIN column %d, page %d\n", column, page_addr);
        /* Just grab some parameters, then wait until
         * PAGEPROG to do the actual operation.
         */
        priv->cmd_cache.page = page_addr;
        priv->cmd_cache.column = column;
        /* Prepare DMA buffer for data. We don't yet know
         * how much data there is, so set size to max.
         */
        cad_init_dmabuf(priv, DMA_BUF_SIZE);
        break;
    case NAND_CMD_PAGEPROG:
        /* Used for both main area and oob */
        pr_debug("PAGEPROG page %d, column %d, w/oob %d, raw %d\n", priv->cmd_cache.page,
                 priv->cmd_cache.column, priv->cmd_cache.oob_required, priv->cmd_cache.write_raw);

        cad_write_mode(priv, priv->cmd_cache.page, priv->cmd_cache.column,
                       priv->cmd_cache.oob_required, priv->cmd_cache.write_raw);
        break;
    case NAND_CMD_READID:
        pr_debug("READID (0x%02x)\n", column);

        /* Read specified ID bytes */
        /* 0x00 would be NAND_READ_ID_ADDR_STD
         * 0x20 would be NAND_READ_ID_ADDR_ONFI,
         * but NAND subsystem knows this and sends us the
         * address values directly
         */
        cad_write(priv, column, ADDR0_COL_REG);

        cad_init_siu_fifo(priv, column == NAND_READ_ID_ADDR_STD ? 5 : 4);
        cad_init_dmabuf(priv, column == NAND_READ_ID_ADDR_STD ? 5 : 4);
        cad_command_and_wait(priv, COMMAND_READ_ID_SIU_FIFO, INT_STATUS_MEM0_RDY_INT_FL);

        for (i = 0; i < priv->dma.bytes_left; i += 4)
        {
            val = cad_read_fifo(priv);
            memcpy(priv->dma.ptr + i, &val, 4);
        }
        pr_debug("  -- Get ID (");
        for (i = 0; i < priv->dma.bytes_left; i += 1)
        {
            pr_debug("%02x", *(u8 *)(priv->dma.ptr + i));
        }
        pr_debug(")\n");

        break;
    case NAND_CMD_STATUS:
        pr_debug("STATUS, defer to later read byte\n");
        /* Don't do anything now, wait until we need to
         * actually read status.
         */
        break;
    default:
        pr_debug("Unhandled command 0x%02x (col %d, page addr %d)\n", command, column, page_addr);
        break;
    }
}

static int cad_init_resources(struct cadence_nfc *priv)
{
    if (priv->use_mode == NFC_INTERNAL_DMA)
    {
        pr_debug("use internal dma mode!\n");
    }
    else if (priv->use_mode == NFC_SIU_FIFO)
    {
        pr_debug("use siu fifo mode!\n");
    }
    else
    {
        pr_debug("nfc_use_mode error!\n");
        return -1;
    }

    priv->dma.buf = (unsigned char *)malloc(DMA_BUF_SIZE);
    if (priv->dma.buf == NULL)
    {
        printf("Malloc memory failed!\n");
        return -ENOMEM;
    }

    priv->dma.phys = (u32)priv->dma.buf;
    memset(priv->dma.buf, 0, DMA_BUF_SIZE);

    if (priv->dma.buf == NULL)
    {
        pr_debug("dma_alloc_coherent failed!\n");
        return -1;
    }

    return 0;
}

#ifdef UPDATE_NFC_TIMING
/* Write timing setup to controller */
static void cad_setup_timing(struct cadence_nfc *priv)
{
    struct cad_setup *cad_setup = &priv->setup;

    cad_write(priv, cad_setup->timings.time_seq_0, TIME_SEQ_0_REG);
    cad_write(priv, cad_setup->timings.time_seq_1, TIME_SEQ_1_REG);
    cad_write(priv, cad_setup->timings.timings_asyn, TIMINGS_ASYN_REG);
    cad_write(priv, cad_setup->timings.time_gen_seq_0, TIME_GEN_SEQ_0_REG);
    cad_write(priv, cad_setup->timings.time_gen_seq_1, TIME_GEN_SEQ_1_REG);
    cad_write(priv, cad_setup->timings.time_gen_seq_2, TIME_GEN_SEQ_2_REG);
    cad_write(priv, cad_setup->timings.time_gen_seq_3, TIME_GEN_SEQ_3_REG);
}

static void cad_update_timing(struct cadence_nfc *priv, struct nfc_timing *time)
{
    u32 nfc_clk, cycle;
    u32 twhr, trhw, tadl, tccs;
    u32 trr, twb, tww;
    u32 t0_d0, t0_d1, t0_d2, t0_d3;
    u32 t0_d4, t0_d5, t0_d6, t0_d7;
    u32 t0_d8, t0_d9, t0_d10, t0_d11;
    u32 t0_d12;
    u32 trwh, trwp;
    struct cad_setup *cad_setup = &priv->setup;

    nfc_clk = clk_get_rate(&priv->clk);
    cycle = 1000000000 / nfc_clk; // cycle pre ns
    debug(" -- nfc timeing cycle %d\n", cycle);

    twhr = time->twhr / cycle;
    trhw = time->trhw / cycle;
    tadl = time->tadl / cycle;
    tccs = time->tccs / cycle;
    trr = time->trr / cycle;
    twb = time->twb / cycle;
    tww = time->tww / cycle;
    t0_d0 = time->t0_d0 / cycle;
    t0_d1 = time->t0_d1 / cycle;
    t0_d2 = time->t0_d2 / cycle;
    t0_d3 = time->t0_d3 / cycle;
    t0_d4 = time->t0_d4 / cycle;
    t0_d5 = time->t0_d5 / cycle;
    t0_d6 = time->t0_d6 / cycle;
    t0_d7 = time->t0_d7 / cycle;
    t0_d8 = time->t0_d8 / cycle;
    t0_d9 = time->t0_d9 / cycle;
    t0_d10 = time->t0_d10 / cycle;
    t0_d11 = time->t0_d11 / cycle;
    t0_d12 = time->t0_d12 / cycle;
    trwh = time->trwh / cycle;
    trwp = time->trwp / cycle;

    cad_setup->timings.time_seq_0 =
        ((twhr & 0x3f) << 24) | ((trhw & 0x3f) << 16) | ((tadl & 0x3f) << 8) | ((tccs & 0x3f) << 0);
    cad_setup->timings.time_seq_1 =
        ((tww & 0x3f) << 16) | ((trr & 0x3f) << 8) | ((twb & 0x3f) << 0);
    cad_setup->timings.time_gen_seq_0 = ((t0_d3 & 0x3f) << 24) | ((t0_d2 & 0x3f) << 16) |
                                        ((t0_d1 & 0x3f) << 8) | ((t0_d0 & 0x3f) << 0);
    cad_setup->timings.time_gen_seq_1 = ((t0_d7 & 0x3f) << 24) | ((t0_d6 & 0x3f) << 16) |
                                        ((t0_d5 & 0x3f) << 8) | ((t0_d4 & 0x3f) << 0);
    cad_setup->timings.time_gen_seq_2 = ((t0_d11 & 0x3f) << 24) | ((t0_d10 & 0x3f) << 16) |
                                        ((t0_d9 & 0x3f) << 8) | ((t0_d8 & 0x3f) << 0);
    cad_setup->timings.time_gen_seq_3 = ((t0_d12 & 0x3f) << 24);
    cad_setup->timings.timings_asyn = ((trwh & 0xf) << 4) | ((trwp & 0xf) << 0);

    cad_setup_timing(priv);
}
#endif

/*wrapper for  nfc controller and mtd*/
static inline void nfc_cmdfunc(struct mtd_info *mtd, unsigned command, int column, int page_addr)
{
    struct nand_chip *chip = mtd_to_nand(mtd);
    struct cadence_nfc *priv = nand_get_controller_data(chip);

    cad_nand_command(priv, command, column, page_addr);
}

static inline int nfc_dev_ready(struct mtd_info *mtd)
{
    struct nand_chip *chip = mtd_to_nand(mtd);
    struct cadence_nfc *priv = nand_get_controller_data(chip);

    return cad_dev_ready(priv);
}

static inline uint8_t nfc_read_byte(struct mtd_info *mtd)
{
    struct nand_chip *chip = mtd_to_nand(mtd);
    struct cadence_nfc *priv = nand_get_controller_data(chip);

    return cad_read_byte(priv);
}

static inline void nfc_write_buf(struct mtd_info *mtd, const uint8_t *buf, int len)
{
    struct nand_chip *chip = mtd_to_nand(mtd);
    struct cadence_nfc *priv = nand_get_controller_data(chip);

    cad_write_dmabuf(priv, buf, len);
}

static inline void nfc_read_buf(struct mtd_info *mtd, uint8_t *buf, int len)
{
    struct nand_chip *chip = mtd_to_nand(mtd);
    struct cadence_nfc *priv = nand_get_controller_data(chip);

    cad_read_buf(priv, buf, len);
}

static inline int nfc_read_page(struct mtd_info *mtd, struct nand_chip *chip, uint8_t *buf,
                                int oob_required, int page)
{
    struct cadence_nfc *priv = nand_get_controller_data(chip);

    pr_debug("read page %d, oob_reqired %d\n", page, oob_required);

    return cad_read_page_hwecc(priv, buf, oob_required, page);
}

static inline int nfc_read_page_raw(struct mtd_info *mtd, struct nand_chip *chip, uint8_t *buf,
                                    int oob_required, int page)
{
    struct cadence_nfc *priv = nand_get_controller_data(chip);

    pr_debug("read raw page %d, oob_reqired %d\n", page, oob_required);

    return cad_read_page_raw(priv, buf, oob_required, page);
}

static inline int nfc_write_page(struct mtd_info *mtd, struct nand_chip *chip, const uint8_t *buf,
                                 int oob_required, int page)
{
    struct cadence_nfc *priv = nand_get_controller_data(chip);

    pr_debug("write page %d, oob_required %d\n", page, oob_required);

    return cad_write_page_hwecc(priv, buf, oob_required);
}

static inline int nfc_write_page_raw(struct mtd_info *mtd, struct nand_chip *chip,
                                     const uint8_t *buf, int oob_required, int page)
{
    struct cadence_nfc *priv = nand_get_controller_data(chip);

    pr_debug("write raw page %d, oob_required %d\n", page, oob_required);

    return cad_write_page_raw(priv, buf, oob_required);
}

static inline int nfc_write_oob(struct mtd_info *mtd, struct nand_chip *chip, int page)
{
    struct cadence_nfc *priv = nand_get_controller_data(chip);

    pr_debug("write oob on page %d \n", page);

    return cad_write_oob(priv, page);
}

static inline void nfc_select_chip(struct mtd_info *mtd, int chip)
{
    /* Nowdays, it only support one chip*/
}

static int nand_init_chip(struct cadence_nfc *priv, int i)
{
    struct nand_chip *nand = &nand_chip[i];
    struct mtd_info *mtd = nand_to_mtd(nand);
    ulong base_addr;

    nand_set_controller_data(nand, priv);
    base_addr = (ulong)priv->regs;

    priv->config.mem_ctrl |= MEM_CTRL_BANK_SEL(priv->cs); // disable write protect
    priv->config.mem_ctrl |= MEM_CTRL_MEM_CE(priv->cs);   // select cs
    cad_config(priv, NULL);

    nand->IO_ADDR_R = nand->IO_ADDR_W = (void __iomem *)base_addr;

    /* Our interface to the mtd API */
    nand->cmdfunc = nfc_cmdfunc;
    nand->dev_ready = nfc_dev_ready;
    nand->read_byte = nfc_read_byte;
    nand->read_buf = nfc_read_buf;
    nand->write_buf = nfc_write_buf;
    nand->block_markbad = nfc_markbad;
    nand->select_chip = nfc_select_chip;

    if (nand_scan_ident(mtd, 1, NULL) < 0)
    {
        printf("%s: nand flash init failed\n", __func__);
        return -1;
    }

    if (priv->setup.ondie_ecc == 1)
    {
        nand->ecc.mode = NAND_ECC_HW;

        nand->ecc.read_page = nfc_read_page_raw;
        nand->ecc.read_page_raw = nfc_read_page_raw;
        nand->ecc.write_page = nfc_write_page_raw;
        nand->ecc.write_page_raw = nfc_write_page_raw;
        nand->ecc.write_oob = nfc_write_oob;
    }
    else
    {
        nand->ecc.mode = NAND_ECC_HW;

        nand->ecc.read_page = nfc_read_page;
        nand->ecc.read_page_raw = nfc_read_page_raw;
        nand->ecc.write_page = nfc_write_page;
        nand->ecc.write_page_raw = nfc_write_page_raw;
        nand->ecc.write_oob = nfc_write_oob;
    }

    nand->ecc.size = priv->setup.ecc_blksize;
    nand->ecc.strength = priv->setup.ecc_strength;
    if (priv->setup.on_flash_bbt)
        nand->bbt_options |= NAND_BBT_USE_FLASH;

    /* Second phase scan */
    if (nand_scan_tail(mtd))
    {
        printf("%s: nand_scan_tail failed\n", __func__);
        return -1;
    }

    /* ECC Config */
    if (mtd->oobsize <= 64)
    {
        nand->ecc.strength = priv->setup.ecc_strength = 4;
        nand->ecc.total = 8 * nand->ecc.steps;
    }
    else
    {
        nand->ecc.strength = priv->setup.ecc_strength = 8;
        nand->ecc.total = 14 * nand->ecc.steps;
    }
    priv->config.ecc_offset = mtd->writesize + mtd->oobsize - nand->ecc.total;
    if (nand->options & SP_OPTIONS16)
    {
        priv->config.ecc_offset = priv->config.ecc_offset >> 1;
    }
    priv->config.ecc_ctrl =
        ECC_CTRL_ECC_THRESHOLD(nand->ecc.strength) | ECC_CTRL_ECC_CAP(nand->ecc.strength);
    pr_debug("ecc cap=%d, ecc offset=%ld, ecc total bytes=%d\n", nand->ecc.strength,
             priv->config.ecc_offset, nand->ecc.total);

    /* Control Config */
    priv->config.control = CONTROL_ECC_BLOCK_SIZE(nand->ecc.size) |
                           CONTROL_BLOCK_SIZE(1 << (nand->phys_erase_shift - nand->page_shift));
    if (nand->options & SP_OPTIONS16)
    {
        priv->config.control |= CONTROL_IO_WIDTH_16;
        pr_debug("nand flash bus width is 16\n");
    }

    cad_config(priv, NULL);

    pr_debug("nfc info: control=0x%08x \n", cad_read(priv, CONTROL_REG));

    return nand_register(i, mtd);
}

void board_nand_init()
{
    int ret;
    struct udevice *dev;
    struct cadence_nfc *priv;
    int i;

    ret = uclass_get_device_by_driver(UCLASS_MTD, DM_GET_DRIVER(fmsh_nand_dt), &dev);
    if (ret == -ENODEV)
    {
        pr_debug("Cannot find nand OF node\n");
        return;
    }
    else if (ret)
    {
        printf("Failed to initialize Cadence NAND controller. (error %d)\n", ret);
        return;
    }

    priv = dev_get_priv(dev);
    for (i = 0; i < CONFIG_SYS_MAX_NAND_DEVICE; i++)
        nand_init_chip(priv, i);
}

static const char *const nand_ecc_modes[] = {
    [NAND_ECC_NONE] = "none",
    [NAND_ECC_SOFT] = "soft",
    [NAND_ECC_HW] = "hw",
    [NAND_ECC_HW_SYNDROME] = "hw_syndrome",
    [NAND_ECC_HW_OOB_FIRST] = "hw_oob_first",
    [NAND_ECC_SOFT_BCH] = "soft_bch",
};

static int of_get_nand_ecc_mode(ofnode node)
{
    const char *pm;
    int i;

    pm = ofnode_read_string(node, "nand-ecc-mode");
    if (pm == NULL)
        return -ENODEV;

    for (i = 0; i < ARRAY_SIZE(nand_ecc_modes); i++)
        if (!strcasecmp(pm, nand_ecc_modes[i]))
            return i;

    return -ENODEV;
}

static int fmsh_dt_probe(struct udevice *dev)
{
    struct cadence_nfc *priv = dev_get_priv(dev);
    struct resource res;
    int ret;

    ret = dev_read_resource(dev, 0, &res);
    if (ret)
        return ret;

    priv->regs = devm_ioremap(dev, res.start, resource_size(&res));

    ret = clk_get_by_name(dev, "nfc_ref", &priv->clk);
    if (ret < 0)
    {
        dev_err(dev, "failed to get ref clock\n");
        return ret;
    }

    ret = clk_get_by_name(dev, "pclk", &priv->pclk);
    if (ret < 0)
    {
        dev_err(dev, "failed to get pclk clock\n");
        return ret;
    }

    clk_enable(&priv->clk);
    clk_enable(&priv->pclk);
    debug("-- nfc base regs: 0x%p, clk %ld\n", priv->regs, clk_get_rate(&priv->clk));

    if (!strcmp(ofnode_read_string(dev_ofnode(dev), "nand-use-mode"), "dma"))
    {
        priv->use_mode = NFC_INTERNAL_DMA;
    }
    else
    {
        priv->use_mode = NFC_SIU_FIFO;
    }
    debug("-- nfc use mode: %s\n", priv->use_mode ? "siu" : "dma");

    priv->setup.ecc_mode = of_get_nand_ecc_mode(dev_ofnode(dev));
    if (priv->setup.ecc_mode < 0)
    {
        priv->setup.ecc_mode = 2;
    }
    debug("-- nfc ecc_mode: %d\n", priv->setup.ecc_mode);

    priv->setup.ecc_strength = ofnode_read_u32_default(dev_ofnode(dev), "nand-ecc-strength", 8);
    priv->setup.ecc_blksize = ofnode_read_u32_default(dev_ofnode(dev), "nand-ecc-step-size", 512);
    priv->setup.ecc_step_ds = ofnode_read_u32_default(dev_ofnode(dev), "nand-ecc-step-size", 512);
    priv->setup.ondie_ecc = ofnode_read_u32_default(dev_ofnode(dev), "nand-ondie-ecc", 0);
    priv->setup.on_flash_bbt = ofnode_read_u32_default(dev_ofnode(dev), "nand-on-flash-bbt", 0);

    debug("-- nfc support ondie_ecc: %d\n", priv->setup.ondie_ecc);

    /* Initialize interrupts and DMA etc. */
    ret = cad_init_resources(priv);
    if (ret)
        return ret;

        /* setting timing */
        // memcpy(&priv->setup.timings, &default_mode0_pll_enabled, sizeof(struct cad_timings));
        // cad_setup_timing(priv);
#ifdef UPDATE_NFC_TIMING
    cad_update_timing(priv, &default_nfc_timing);
#endif

    return 0;
}

static const struct udevice_id fmsh_nand_dt_ids[] = {{
                                                         .compatible = "cadence,fmql_nfc",
                                                         .compatible = "fmsh,psoc-nfc",
                                                     },
                                                     {/* sentinel */}};

#endif /* TTOS_ENABLE_UBOOT_FMSH_NFC */
