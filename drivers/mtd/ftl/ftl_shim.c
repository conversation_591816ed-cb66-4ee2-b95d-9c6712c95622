#include <errno.h>
#include <fs/fs.h>
#include <mtd/mtd.h>
#include <string.h>

#define KLOG_TAG "mtdblk-shim"
#include <klog.h>

/* Minimal, read-only MTD->block adapter to satisfy mtd_proxy() without full FTL.
 * This registers a temporary block device at 'path' that forwards read() to
 * the underlying MTD via MTD_READ with 512-byte sector semantics. Writes are
 * not implemented (return -ENOSYS).
 */

struct mtdblk_shim
{
    struct mtd_dev_s *mtd;
    struct mtd_geometry_s geo;
    unsigned int secsize; /* logical sector size presented to upper layers */
};

static int mtdblk_open(struct inode *inode)
{
    (void)inode;
    return 0;
}

static int mtdblk_close(struct inode *inode)
{
    (void)inode;
    return 0;
}

static ssize_t mtdblk_read(struct inode *inode, unsigned char *buffer, blkcnt_t start_sector,
                           unsigned int nsectors)
{
    struct mtdblk_shim *shim = (struct mtdblk_shim *)inode->i_private;
    if (!shim || !shim->mtd)
        return -ENODEV;

    size_t bytes = (size_t)nsectors * shim->secsize;
    off_t off = (off_t)start_sector * shim->secsize;

    /* Use byte-oriented MTD_READ; our MTD driver supports unaligned byte reads */
    return MTD_READ(shim->mtd, off, bytes, buffer);
}

static ssize_t mtdblk_write(struct inode *inode, const unsigned char *buffer, blkcnt_t start_sector,
                            unsigned int nsectors)
{
    struct mtdblk_shim *shim = (struct mtdblk_shim *)inode->i_private;
    if (!shim || !shim->mtd)
        return -ENODEV;

    /* We only support full-page aligned writes for safety */
    const uint32_t pagesz = shim->geo.blocksize ? shim->geo.blocksize : 2048;
    const uint32_t secsz = shim->secsize ? shim->secsize : 512;
    const uint32_t secs_per_page = pagesz / secsz;

    /* Alignment checks */
    if ((start_sector % secs_per_page) != 0)
        return -EINVAL;
    if ((nsectors % secs_per_page) != 0)
        return -EINVAL;

    off_t page_index = start_sector / secs_per_page;
    unsigned int npages = nsectors / secs_per_page;

    const unsigned char *src = buffer;
    for (unsigned int i = 0; i < npages; i++)
    {
        ssize_t ret = MTD_BWRITE(shim->mtd, page_index + i, 1, (const uint8_t *)src);
        if (ret < 0)
            return ret;
        if (ret != 1)
            return -EIO;
        src += pagesz;
    }

    return (ssize_t)nsectors;
}

static int mtdblk_geometry(struct inode *inode, struct geometry *geo)
{
    if (!inode || !geo)
        return -EINVAL;
    struct mtdblk_shim *shim = (struct mtdblk_shim *)inode->i_private;
    if (!shim || !shim->mtd)
        return -ENODEV;

    /* Ensure we have MTD geometry cached */
    if (shim->geo.erasesize == 0 || shim->geo.neraseblocks == 0)
    {
        struct mtd_geometry_s g;
        if (MTD_IOCTL(shim->mtd, MTDIOC_GEOMETRY, (unsigned long)&g) == 0)
        {
            shim->geo = g;
        }
    }

    uint64_t total_bytes = (uint64_t)shim->geo.erasesize * (uint64_t)shim->geo.neraseblocks;
    uint32_t sec = shim->secsize ? shim->secsize : 512;
    blkcnt_t nsectors = (blkcnt_t)(total_bytes / sec);

    geo->geo_available = true;
    geo->geo_mediachanged = false;
    /* 允许写：具体写保护由分区权限与底层 MTD 的 bwrite/erase 控制。
       fsbl/uboot/env 分区已用 0440 注册，不可写；rootfs 用 0660，可写。 */
    geo->geo_writeenabled = true;
    geo->geo_nsectors = nsectors;
    geo->geo_sectorsize = sec;
    return 0;
}

static int mtdblk_ioctl(struct inode *inode, int cmd, unsigned long arg)
{
    struct mtdblk_shim *shim = (struct mtdblk_shim *)inode->i_private;
    if (!shim || !shim->mtd)
        return -ENODEV;
    /* Try forwarding MTD ioctls if someone issues them on the block node */
    return MTD_IOCTL(shim->mtd, cmd, arg);
}

static const struct block_operations g_mtdblk_ops = {
    .open = mtdblk_open,
    .close = mtdblk_close,
    .read = mtdblk_read,
    .write = mtdblk_write,
    .geometry = mtdblk_geometry,
    .ioctl = mtdblk_ioctl,
};

int ftl_initialize_by_path(const char *path, struct mtd_dev_s *mtd)
{
    if (!path || !mtd)
        return -EINVAL;
    struct mtdblk_shim *shim = (struct mtdblk_shim *)malloc(sizeof(*shim));
    if (!shim)
        return -ENOMEM;
    memset(shim, 0, sizeof(*shim));

    shim->mtd = mtd;
    shim->secsize = 512; /* present 512-byte logical sectors */

    /* Cache MTD geometry for potential conversions */
    struct mtd_geometry_s geo;
    if (MTD_IOCTL(mtd, MTDIOC_GEOMETRY, (unsigned long)&geo) == 0)
    {
        shim->geo = geo;
    }
    else
    {
        memset(&shim->geo, 0, sizeof(shim->geo));
        strncpy(shim->geo.model, "mtdblk-shim", sizeof(shim->geo.model) - 1);
    }

    /* Register a temporary block device at the requested path */
    int ret = register_blockdriver(path, &g_mtdblk_ops, 0660, shim);
    if (ret < 0)
    {
        free(shim);
        return ret;
    }
    return 0;
}

int ftl_initialize(int minor, struct mtd_dev_s *mtd)
{
    (void)minor;
    (void)mtd;
    return -ENOSYS;
}
